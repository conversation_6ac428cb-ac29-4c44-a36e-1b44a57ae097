from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages

from membership.decorators import membership_required
from Library.views import send_sms
from .models import *
from librarian.models import *
from subLibrarian.models import *
import os
from django.conf import settings
from django.http import HttpResponse, JsonResponse
import random
from utils.notifications import  send_dynamic_email, send_email_thread
from datetime import datetime, timedelta
from django.contrib.auth.decorators import login_required
import threading
from django.urls import reverse
from django.db import IntegrityError

from wallet_and_transactions.models import *
from Library.user_auth import *
from .signals import set_student_due_color
import pyshorteners
from .models import ShortenedURL, RegistrationFeedback

from django.db.models import Max
from datetime import timedelta
from django.utils import timezone

from django.db.models import Max
from datetime import timedelta
from django.utils import timezone
from urllib.parse import urlparse



@login_required(login_url="/librarian/login/")
@membership_required("Basic")
def student_create(request):
    states = States.objects.all()
    courses = Courses.objects.all()
    try:

        role = request.user.groups.all()[0].name

        if request.method == "POST":
            userid = request.user.id
            course_id = request.POST.get("course")
            name = request.POST.get("name").capitalize()
            fname = request.POST.get("fathername").capitalize()
            age = request.POST.get("age")
            gender = request.POST.get("gender")
            email = request.POST.get("email")
            mobile = request.POST.get("phone")
            locality = request.POST.get("locality")
            city = request.POST.get("city")
            state_id = request.POST.get("state")
            rdate = request.POST.get("rdate")
            registration_fee = request.POST.get("regfees")
            image = request.FILES.get("image")

            # Proof of identity fields
            identity_type = request.POST.get("identity_type")
            identity_number = request.POST.get("identity_number")

            # parsed_date = datetime.strptime(rdate, "%d-%m-%Y")

            age = int(age) if age else None
            fname = fname if fname else None

            state = States.objects.get(id=state_id)

            try:
                sublibrarian = Sublibrarian_param.objects.get(user=userid)
                librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                librarian = Librarian_param.objects.get(user=userid)
                sublibrarian = None

            # Validate identity number format and uniqueness if provided
            if identity_number and identity_number.strip():
                identity_number = identity_number.strip().upper()

                # Validate format based on identity type
                if identity_type:
                    import re
                    valid_format = False

                    if identity_type == 'aadhaar':
                        valid_format = re.match(r'^\d{12}$', identity_number)
                        if not valid_format:
                            messages.error(request, "Aadhaar number must be exactly 12 digits.")
                    elif identity_type == 'pan':
                        valid_format = re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', identity_number)
                        if not valid_format:
                            messages.error(request, "PAN must be in format: 5 letters, 4 digits, 1 letter (e.g., **********).")
                    elif identity_type == 'driving_license':
                        valid_format = re.match(r'^[A-Z0-9]{15}$', identity_number)
                        if not valid_format:
                            messages.error(request, "Driving License must be exactly 15 alphanumeric characters.")
                    elif identity_type == 'voter_id':
                        valid_format = re.match(r'^[A-Z0-9]{10}$', identity_number)
                        if not valid_format:
                            messages.error(request, "Voter ID must be exactly 10 alphanumeric characters.")

                    if not valid_format:
                        return render(request, "student_create.html", {
                            "courses": courses,
                            "states": states,
                            "librarian": librarian,
                        })

                # Check uniqueness
                if StudentData.objects.filter(identity_number=identity_number).exists():
                    messages.error(request, "A student with this identity number already exists.")
                    return render(request, "student_create.html", {
                        "courses": courses,
                        "states": states,
                        "librarian": librarian,
                    })
                if TempStudentData.objects.filter(identity_number=identity_number).exists():
                    messages.error(request, "A student with this identity number is already in the registration process.")
                    return render(request, "student_create.html", {
                        "courses": courses,
                        "states": states,
                        "librarian": librarian,
                    })

            otp = random.randint(100000, 999999)
            request.session["otp"] = otp
            request.session["email"] = email
            request.session["student_data"] = {
                "librarian_id": librarian.id,  # type: ignore
                "sublibrarian_id": sublibrarian.id if sublibrarian else None,  # type: ignore
                "course": course_id,
                "name": name,
                "f_name": fname,
                "age": age,
                "gender": gender,
                "mobile": mobile,
                "locality": locality,
                "city": city,
                "state_id": state.id,  # type: ignore
                "registration_date": rdate,
                "registration_fee": registration_fee,
                "image": image.name if image else None,
                "identity_type": identity_type if identity_type else None,
                "identity_number": identity_number if identity_number else None,
            }

            # Email OTP Verification Code
            subject = "OTP Verification"
            template_name = "email_otp.html"
            context = {"student_name": name, "otp": otp, "librarian": librarian}

            send_dynamic_email(subject, template_name, context, email)
            # Check user wallet balance before sending SMS
            user = request.user
            note = f"OTP sent to {name} (Contact: {mobile})"

            if role == "librarian":

                wallet_response = user_wallet(user, note)
            else:
                sublibrarian = Sublibrarian_param.objects.get(user=user)
                librarian = sublibrarian.librarian.user
                wallet_response = user_wallet(librarian, note)

            if wallet_response["status"] == "success":
                # Send SMS
                sender = "LBRIAN"
                number = mobile
                message = f"Dear Student, Thank you so much for choosing librainian.com, Your OTP to get registered {otp}. Regards PINAK VENTURE LBRIAN"
                template_id = 165734

                p = send_sms(str(sender), str(number), str(message), str(template_id))
                messages.success(
                    request,
                    "OTP has been sent to your email and mobile. Please check your email.",
                )
            else:
                # Handle insufficient balance or other errors
                if wallet_response["status"] == "failure":
                    messages.warning(
                        request,
                        "Insufficient wallet balance. Unfortunately, the OTP could not be sent via SMS. Please check student's email for the OTP",
                    )
                else:
                    messages.error(
                        request,
                        f"An error occurred: {wallet_response['message']}",
                    )

            return redirect("/students/verify_otp/")

        else:
            userid = request.user.id
            try:
                sublibrarian = Sublibrarian_param.objects.get(user=userid)
                librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                librarian = Librarian_param.objects.get(user=userid)
                sublibrarian = None

            students = StudentData.objects.filter(librarian=librarian)

            return render(
                request,
                "student_create.html",
                {
                    "students": students,
                    "states": states,
                    "courses": courses,
                    "role": role,
                },
            )

    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")

    # Get librarian for template context
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=request.user.id)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        librarian = Librarian_param.objects.get(user=request.user.id)

    return render(request, "student_create.html",
                {
                    "states": states,
                    "courses": courses,
                    "librarian": librarian,
                },)


def resend_otp(request):
    # Check if the OTP exists in the session
    otp = request.session.get("otp")
    name = request.session.get("student_name")
    email = request.session.get("email")
    mobile = request.session.get("mobile")
    role = request.session.get("role")
    librarian = request.session.get("librarian")

    if not otp or not email or not mobile or not name or not role:
        messages.error(
            request, "Session expired or invalid data. Please restart the process."
        )
        return redirect("your_redirect_url")  # Replace with the appropriate URL

    # Resend Email OTP
    subject = "OTP Verification - Resend"
    template_name = "email_otp.html"
    context = {"student_name": name, "otp": otp, "librarian": librarian}
    send_dynamic_email(subject, template_name, context, email)

    # Check user wallet balance before sending SMS
    user = request.user
    note = f"OTP resent to {name} (Contact: {mobile})"

    if role == "librarian":
        wallet_response = user_wallet(user, note)
    else:
        sublibrarian = Sublibrarian_param.objects.get(user=user)
        librarian = sublibrarian.librarian.user
        wallet_response = user_wallet(librarian, note)

    if wallet_response["status"] == "success":
        # Send SMS
        sender = "LBRIAN"
        number = mobile
        message = f"Dear Student, Thank you so much for choosing librainian.com. Your OTP to get registered is {otp}. Regards, PINAK VENTURE LBRIAN"
        template_id = 165734

        send_sms(str(sender), str(number), str(message), str(template_id))

        messages.success(
            request,
            "OTP has been resent to your email and mobile. Please check your email.",
        )
    else:
        # Handle insufficient balance or other errors
        if wallet_response["status"] == "failure":
            messages.warning(
                request,
                "Insufficient wallet balance. Unfortunately, the OTP could not be resent via SMS. Please check student's email for the OTP.",
            )
        else:
            messages.error(
                request,
                f"An error occurred: {wallet_response['message']}",
            )

    return redirect("/students/verify_otp/")


def verify_otp(request):
    role = request.user.groups.all()[0].name
    if request.method == "POST":
        otp = "".join(request.POST.get(f"otp{i}") for i in range(1, 7))
        stored_otp = request.session.get("otp")

        email = request.session.get("email")
        student_data = request.session.get("student_data")

        # Validate OTP fields
        if len(otp) != 6:
            messages.error(request, "Please complete all OTP fields.")
            return render(request, "student_verify_otp.html", {"role": role})

        # Validate stored OTP
        if stored_otp is None:
            messages.error(request, "OTP has expired or is not set.")
            return render(request, "student_verify_otp.html", {"role": role})

        # Validate OTP
        if otp != str(stored_otp):
            messages.error(request, "Invalid OTP. Please try again.")
            return render(request, "student_verify_otp.html", {"role": role})

        try:
            # OTP is correct, create the student record
            librarian = Librarian_param.objects.get(id=student_data["librarian_id"])
            sublibrarian = None
            if student_data.get("sublibrarian_id"):
                sublibrarian = Sublibrarian_param.objects.get(
                    id=student_data["sublibrarian_id"]
                )

            state = States.objects.get(id=student_data["state_id"])
            course = Courses.objects.get(id=student_data["course"])

            try:
                student = StudentData.objects.create(
                    librarian=librarian,
                    sublibrarian=sublibrarian,
                    course=course,
                    name=student_data["name"],
                    f_name=student_data["f_name"],
                    age=student_data["age"],
                    gender=student_data["gender"],
                    email=email,
                    mobile=student_data["mobile"],
                    locality=student_data["locality"],
                    city=student_data["city"],
                    state=state,
                    registration_date=student_data["registration_date"],
                    registration_fee=student_data["registration_fee"],
                    image=student_data["image"],
                    identity_type=student_data.get("identity_type"),
                    identity_number=student_data.get("identity_number"),
                )
                # Clear session data
                request.session.pop("otp", None)
                request.session.pop("email", None)
                request.session.pop("student_data", None)

                # Redirect to invoice creation page
                return redirect(f"/students/create_registration/{student.slug}/")
            except IntegrityError as e:
                error_message = str(e)
                if "unique constraint" in error_message:
                    messages.error(
                        request,
                        "A student with this information already exists. Please check the details and try again.",
                    )
                return render(request, "student_verify_otp.html", {"role": role})

        except (
            Librarian_param.DoesNotExist,
            Sublibrarian_param.DoesNotExist,
            States.DoesNotExist,
            Courses.DoesNotExist,
        ) as e:
            # Handle the case where related objects do not exist
            messages.error(
                request, f"An error {e} occurred while processing your request."
            )
            return render(request, "student_verify_otp.html", {"role": role})
    else:
        return render(request, "student_verify_otp.html", {"role": role})


@login_required(login_url="/librarian/login/")
def create_registration_fee(request, slug):
    student = get_object_or_404(StudentData, slug=slug)
    role = request.user.groups.all()[0].name

    if request.method == "POST":
        is_paid = request.POST.get("is_paid", "False") == "True"
        try:
            if is_paid:
                reg_fee, created = RegistrationFee.objects.get_or_create(
                    student=student
                )
                reg_fee.is_paid = True
                reg_fee.save()

                messages.success(
                    request,
                    f"{student.name}'s registration fee has been successfully marked as paid.",
                )
            else:
                reg_fee, created = RegistrationFee.objects.get_or_create(
                    student=student
                )
                reg_fee.is_paid = False
                reg_fee.save()
                messages.success(
                    request,
                    f"{student.name}'s registration fee status has been recorded.",
                )

        except Exception as e:
            messages.error(
                request,
                f"There was an error processing the registration fee for {student.name}: {str(e)}",
            )

        # Sending the invoice email
        subject = "Welcome to library mail"
        template_name = "Welcome_mail_student.html"
        context = {"student": student}

        send_dynamic_email(subject, template_name, context, student.email)

        return redirect(f"/students/create_invoice/{student.slug}/")
        # return redirect("create_invoice", student_slug=student.slug)

    return render(
        request,
        "student_registration_fee.html",
        {"student": student, "role": role},
    )


@login_required(login_url="/librarian/login/")
def send_student_email(request, slug):
    student = get_object_or_404(StudentData, slug=slug)
    # Sending the invoice email
    subject = "Welcome to library mail"
    template_name = "Welcome_mail_student.html"
    context = {"student": student}

    send_dynamic_email(subject, template_name, context, student.email)
    return redirect(reverse("library_dashboard"))


@login_required(login_url="/librarian/login/")
def create_invoice(request, slug):
    student = get_object_or_404(StudentData, slug=slug)
    invoice = Invoice.objects.filter(student=student).last()
    registration_fee = RegistrationFee.objects.filter(student=student).last()
    role = request.user.groups.all()[0].name

    if request.method == "POST":
        due_date = request.POST.get("due_date")
        shift_ids = request.POST.getlist("shifts")
        month_ids = request.POST.getlist("months")
        discount_amount = request.POST.get("discount_amount")
        mode_pay = request.POST.get("mode_pay")
        description = request.POST.get("description")

        # Billing cycle fields
        billing_cycle = request.POST.get("billing_cycle", "monthly")
        billing_units = int(request.POST.get("billing_units", len(month_ids) if month_ids else 1))

        # Partial payment fields
        is_partial_payment = request.POST.get("is_partial_payment") == "on"
        partial_amount = request.POST.get("partial_amount")
        next_payment_date = request.POST.get("next_payment_date")

        shifts = Shift.objects.filter(id__in=shift_ids)

        # Calculate total amount based on billing cycle
        total_amount = 0
        for shift in shifts:
            if billing_cycle in ['half_weekly', 'half_monthly']:
                # Half-cycle billing with 10% premium
                base_cycle = billing_cycle.replace('half_', '')
                rate = shift.get_half_cycle_rate(base_cycle)
            else:
                rate = shift.get_rate_for_billing_cycle(billing_cycle)
            total_amount += rate * billing_units

        # Add registration fee if not paid
        if not registration_fee.is_paid:
            total_amount += student.registration_fee

        if discount_amount:
            total_amount -= int(discount_amount)

        # Validate partial payment amount
        if is_partial_payment and partial_amount:
            partial_amount = int(partial_amount)
            if partial_amount <= 0:
                messages.error(request, "Partial payment amount must be greater than 0.")
                return redirect("create_invoice", slug=slug)
            if partial_amount > total_amount:
                messages.error(request, "Partial payment amount cannot exceed total amount.")
                return redirect("create_invoice", slug=slug)

        # Calculate due date based on billing cycle
        if due_date:
            base_due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
        else:
            base_due_date = datetime.now().date()

        # Calculate days to add based on billing cycle
        if billing_cycle == 'daily':
            days_to_add = billing_units
        elif billing_cycle == 'weekly':
            days_to_add = billing_units * 7
        elif billing_cycle == 'monthly':
            days_to_add = billing_units * 30
        elif billing_cycle == 'half_weekly':
            days_to_add = billing_units * 3.5  # Half week
        elif billing_cycle == 'half_monthly':
            days_to_add = billing_units * 15  # Half month
        else:
            days_to_add = billing_units * 30  # Default to monthly

        due_date = base_due_date + timedelta(days=int(days_to_add))

        # Create invoice with billing cycle information
        invoice = Invoice.objects.create(
            student=student,
            due_date=due_date,
            total_amount=total_amount,
            discount_amount=discount_amount or 0,
            mode_pay=mode_pay,
            description=description,
            billing_type=billing_cycle,
            billing_units=billing_units,
            billing_start_date=base_due_date,
            billing_end_date=due_date,
        )
        invoice.shift.set(shifts)
        invoice.months.set(month_ids)

        # Ensure slug is generated (force save to trigger slug generation)
        if not invoice.slug or not invoice.invoice_id:
            invoice.save()
            # Refresh from database to ensure we have the latest data
            invoice.refresh_from_db()

        # Debug: Print invoice details
        print(f"DEBUG: Invoice ID: {invoice.invoice_id}")
        print(f"DEBUG: Invoice Slug: {invoice.slug}")
        print(f"DEBUG: Invoice PK: {invoice.pk}")

        # If still no slug, generate it manually
        if not invoice.slug and invoice.invoice_id:
            from django.utils.text import slugify
            invoice.slug = slugify(invoice.invoice_id)
            invoice.save()
            print(f"DEBUG: Manually generated slug: {invoice.slug}")

        # Final check
        if not invoice.slug:
            print(f"ERROR: Invoice slug is still empty after all attempts!")
            print(f"ERROR: Invoice details - ID: {invoice.invoice_id}, PK: {invoice.pk}")
            # Try one more time with a fallback slug
            invoice.slug = f"invoice-{invoice.pk}-{timezone.now().strftime('%Y%m%d%H%M%S')}"
            invoice.save()
            print(f"DEBUG: Fallback slug generated: {invoice.slug}")

        # Handle payment creation
        if is_partial_payment and partial_amount:
            # Create partial payment record
            from .models import Payment
            payment = Payment.objects.create(
                invoice=invoice,
                amount_paid=partial_amount,
                payment_mode=mode_pay,
                next_commitment_date=datetime.strptime(next_payment_date, "%Y-%m-%d").date() if next_payment_date else None,
                notes=f"Initial partial payment for invoice {invoice.invoice_id}"
            )
        else:
            # Create full payment record
            from .models import Payment
            payment = Payment.objects.create(
                invoice=invoice,
                amount_paid=total_amount,
                payment_mode=mode_pay,
                notes=f"Full payment for invoice {invoice.invoice_id}"
            )

        # Generate invoice URL and send email
        # Ensure we have a valid slug before generating URL
        if invoice.slug:
            invoice_url = request.build_absolute_uri(
                f"/students/invoice_student/{invoice.slug}/"
            )
        else:
            invoice_url = ""
            print(f"WARNING: Invoice slug is empty for invoice ID: {invoice.invoice_id}")

        # Only send email if we have a valid invoice
        try:
            if invoice.slug:
                send_dynamic_email(
                    "Your Invoice Details",
                    "invoice_email.html",
                    {
                        "student": student,
                        "invoice": invoice,
                        "invoice_url": invoice_url,
                        "invoice_slug": invoice.slug,
                    },
                    student.email,
                )
            else:
                print(f"ERROR: Cannot send email - invoice slug is empty")
        except Exception as e:
            print(f"ERROR: Failed to send invoice email: {e}")
            # Continue with the rest of the process even if email fails

        return redirect("book_seat", invoice_slug=invoice.slug)

    try:
        sublibrarian = Sublibrarian_param.objects.get(user=request.user)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        librarian = Librarian_param.objects.get(user=request.user)
        sublibrarian = None

    shifts = Shift.objects.filter(librarian=librarian)
    months = Months.objects.all()
    seats = Seat.objects.prefetch_related("booking_set").order_by("seat_number")

    return render(
        request,
        "invoice_create.html",
        {
            "student": student,
            "invoice": invoice,
            "shifts": shifts,
            "seats": seats,
            "invoice": invoice,
            "role": role,
            "months": months,
        },
    )


@login_required(login_url="/librarian/login/")
def book_seat(request, invoice_slug):
    invoice = get_object_or_404(Invoice, slug=invoice_slug)
    student = invoice.student
    role = request.user.groups.all()[0].name
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=request.user)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        librarian = Librarian_param.objects.get(user=request.user)
        sublibrarian = None

    # Handle multiple shifts if shift is a ManyToMany field
    shifts = invoice.shift.all()
    bookings = Booking.objects.filter(student=student)

    seats = Seat.objects.filter(
        librarian=librarian, shift__in=shifts, is_available=True
    )
    if request.method == "POST":
        seat_ids = request.POST.get("selectedSeats", "").split(",")

        booked_seats = []
        # Deallocate existing seats before proceeding
        for booking in bookings:
            seat = booking.seat
            seat.is_available = True
            seat.save()
            booking.delete()

        for seat_id in seat_ids:
            # Ensure that the seat_id is stripped of any whitespace
            seat_id = seat_id.strip()
            if seat_id:  # Check if seat_id is not an empty string
                seat = get_object_or_404(Seat, id=seat_id)
                if seat.is_available:
                    booking = Booking.objects.create(
                        student=student, seat=seat, expire_date=invoice.due_date
                    )
                    seat.is_available = False
                    seat.save()
                    booked_seats.append(seat)

        if booked_seats:
            # SMS Notification
            short_url_obj = ShortenedURL.objects.get_or_create(
                original_url=request.build_absolute_uri(
                    f"/students/invoice_student/{invoice.slug}/"
                )
            )[0]

            short_url = short_url_obj.get_short_url(request)

            unique_part = urlparse(short_url).path.rstrip("/").split("/")[-1]

            message = f"Dear Customer, Your Library Subscription of Rs {invoice.total_amount} has been generated. Check details: {'librainian.com/students/s/'}{unique_part} LBRIAN"

            send_sms("LBRIAN", student.mobile, message, template_id=170593)

            messages.success(request, "Invoice created and seats booked successfully!")
            return redirect("invoice_success", slug=invoice.slug)
        else:
            messages.error(request, "Selected seats are already booked.")

    return render(
        request,
        "book_seat.html",
        {
            "student": student,
            "seats": seats,
            "invoice": invoice,
            "role": role,
            "shifts": shifts,
        },
    )


@login_required(login_url="/librarian/login/")
def invoice_success(request, slug):
    invoice = Invoice.objects.get(slug=slug)
    role = request.user.groups.all()[0].name

    return render(
        request,
        "invoice_success.html",
        {"invoice": invoice, "role": role},
    )


def redirect_to_original(request, short_code):
    url_instance = get_object_or_404(ShortenedURL, short_code=short_code)
    return redirect(url_instance.original_url)


@login_required(login_url="/librarian/login/")
def add_payment_page(request, invoice_slug):
    """Display the additional payment page"""
    invoice = get_object_or_404(Invoice, slug=invoice_slug)
    role = request.user.groups.all()[0].name

    # Get payment history for this invoice
    payments = Payment.objects.filter(invoice=invoice).order_by('-payment_date')

    context = {
        'invoice': invoice,
        'student': invoice.student,
        'payments': payments,
        'role': role,
    }

    return render(request, 'add_payment.html', context)


@login_required(login_url="/librarian/login/")
def record_payment(request, invoice_slug):
    """Record additional payment for an existing invoice"""
    invoice = get_object_or_404(Invoice, slug=invoice_slug)

    if request.method == "POST":
        amount_paid = request.POST.get("amount_paid")
        payment_mode = request.POST.get("payment_mode", "Cash")
        next_payment_date = request.POST.get("next_payment_date")
        notes = request.POST.get("notes", "")

        try:
            amount_paid = int(amount_paid)

            # Validation
            if amount_paid <= 0:
                messages.error(request, "Payment amount must be greater than 0.")
                return redirect("add_payment_page", invoice_slug=invoice_slug)

            if amount_paid > invoice.remaining_due:
                messages.error(request, f"Payment amount (₹{amount_paid}) cannot exceed remaining due (₹{invoice.remaining_due}).")
                return redirect("add_payment_page", invoice_slug=invoice_slug)

            # Create payment record
            from .models import Payment
            payment = Payment.objects.create(
                invoice=invoice,
                amount_paid=amount_paid,
                payment_mode=payment_mode,
                next_commitment_date=datetime.strptime(next_payment_date, "%Y-%m-%d").date() if next_payment_date else None,
                notes=notes or f"Payment for invoice {invoice.invoice_id}"
            )

            # Send notification email if payment completes the invoice
            if invoice.is_paid_in_full:
                try:
                    send_dynamic_email(
                        "Payment Completed - Invoice Fully Paid",
                        "payment_completion_email.html",
                        {
                            "student": invoice.student,
                            "invoice": invoice,
                            "payment": payment,
                            "library_name": invoice.student.librarian.library_name if invoice.student.librarian else "Librainian",
                        },
                        invoice.student.email,
                    )
                    messages.success(request, f"Payment of ₹{amount_paid} recorded successfully! Invoice is now fully paid. Confirmation email sent to {invoice.student.email}.")
                except Exception as e:
                    print(f"Email sending error: {str(e)}")
                    messages.success(request, f"Payment of ₹{amount_paid} recorded successfully! Invoice is now fully paid. (Email notification failed)")
            else:
                messages.success(request, f"Payment of ₹{amount_paid} recorded successfully! Remaining balance: ₹{invoice.remaining_due}")

        except ValueError:
            messages.error(request, "Invalid payment amount.")
        except Exception as e:
            messages.error(request, f"Error recording payment: {str(e)}")

    return redirect("add_payment_page", invoice_slug=invoice_slug)


@login_required(login_url="/librarian/login/")
def partial_payments_list(request):
    """List all students with partial payments"""
    try:
        sublibrarian = Sublibrarian_param.objects.get(user=request.user)
        librarian = sublibrarian.librarian
    except Sublibrarian_param.DoesNotExist:
        librarian = Librarian_param.objects.get(user=request.user)

    # Get all invoices with partial payments
    partial_invoices = Invoice.objects.filter(
        student__librarian=librarian,
        payment_status='Partially Paid',
        is_active=True
    ).select_related('student').prefetch_related('payment_set').order_by('-issue_date')

    # Prepare data for template
    invoice_data = []
    for invoice in partial_invoices:
        payments = invoice.payment_set.all()
        last_payment = payments.first() if payments else None

        invoice_data.append({
            'invoice': invoice,
            'student': invoice.student,
            'total_payments': len(payments),
            'last_payment_date': last_payment.payment_date if last_payment else None,
            'next_commitment_date': last_payment.next_commitment_date if last_payment else None,
            'payment_percentage': invoice.get_payment_percentage(),
        })

    role = request.user.groups.all()[0].name if request.user.groups.exists() else None

    return render(request, 'partial_payments_list.html', {
        'invoice_data': invoice_data,
        'role': role,
        'librarian': librarian,
    })


@login_required(login_url="/librarian/login/")
def mark_complete_page(request, invoice_slug):
    """Display the mark complete page"""
    invoice = get_object_or_404(Invoice, slug=invoice_slug)
    role = request.user.groups.all()[0].name

    context = {
        'invoice': invoice,
        'student': invoice.student,
        'role': role,
    }

    return render(request, 'mark_complete.html', context)


@login_required(login_url="/librarian/login/")
def mark_invoice_complete(request, invoice_slug):
    """Mark an invoice as fully paid"""
    if request.method == "POST":
        try:
            invoice = get_object_or_404(Invoice, slug=invoice_slug)

            # Get payment mode from request
            payment_mode = request.POST.get('payment_mode', 'Cash')

            # Check if user has permission
            try:
                sublibrarian = Sublibrarian_param.objects.get(user=request.user)
                librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                librarian = Librarian_param.objects.get(user=request.user)

            if invoice.student.librarian != librarian:
                return JsonResponse({'success': False, 'error': 'Unauthorized'})

            payment = None
            # Handle different scenarios for marking as complete
            if invoice.remaining_due > 0:
                # Create final payment for remaining amount
                from .models import Payment
                payment = Payment.objects.create(
                    invoice=invoice,
                    amount_paid=invoice.remaining_due,
                    payment_mode=payment_mode,
                    notes='Final payment - marked as complete by staff'
                )
            elif invoice.payment_set.count() == 0:
                # For older invoices without payment records, create a full payment record
                from .models import Payment
                payment = Payment.objects.create(
                    invoice=invoice,
                    amount_paid=invoice.total_amount,
                    payment_mode=payment_mode,
                    notes='Full payment - marked as complete by staff (legacy invoice)'
                )
            else:
                # Invoice is already fully paid, just update status
                invoice.is_paid_in_full = True
                invoice.payment_status = 'Paid'
                invoice.remaining_due = 0
                invoice.total_paid = invoice.total_amount
                invoice.save()

            # Send completion email notification
            if invoice.is_paid_in_full:
                try:
                    send_dynamic_email(
                        "Payment Completed - Invoice Fully Paid",
                        "payment_completion_email.html",
                        {
                            "student": invoice.student,
                            "invoice": invoice,
                            "payment": payment or invoice.payment_set.last(),
                            "library_name": invoice.student.librarian.library_name if invoice.student.librarian else "Librainian",
                        },
                        invoice.student.email,
                    )
                    print(f"✅ Completion email sent to {invoice.student.email}")
                except Exception as e:
                    print(f"❌ Failed to send completion email: {str(e)}")

            messages.success(request, f"Invoice marked as complete successfully! Completion email sent to {invoice.student.email}.")

            # Redirect to appropriate dashboard based on user role
            role = request.user.groups.all()[0].name
            if role == 'sublibrarian':
                return redirect('dashboard')
            else:
                return redirect('library_dashboard')

        except Exception as e:
            messages.error(request, f"Error marking invoice as complete: {str(e)}")
            return redirect('mark_complete_page', invoice_slug=invoice_slug)

    return redirect('mark_complete_page', invoice_slug=invoice_slug)


@login_required(login_url="/librarian/login/")
def send_payment_reminder(request, invoice_slug):
    """Send payment reminder email"""
    if request.method == "POST":
        try:
            invoice = get_object_or_404(Invoice, slug=invoice_slug)

            # Check if user has permission
            try:
                sublibrarian = Sublibrarian_param.objects.get(user=request.user)
                librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                librarian = Librarian_param.objects.get(user=request.user)

            if invoice.student.librarian != librarian:
                return JsonResponse({'success': False, 'error': 'Unauthorized'})

            # Calculate days overdue
            from django.utils import timezone
            days_overdue = (timezone.now().date() - invoice.due_date).days

            # Get payment history
            payments = invoice.payment_set.all()

            # Send reminder email
            send_dynamic_email(
                f"Payment Reminder - Invoice #{invoice.invoice_id}",
                "payment_reminder_email.html",
                {
                    "student": invoice.student,
                    "invoice": invoice,
                    "days_overdue": max(0, days_overdue),
                    "payments": payments,
                    "library_name": invoice.student.librarian.library_name,
                },
                invoice.student.email
            )

            return JsonResponse({'success': True})

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


def invoice_student(request, slug):
    invoice = Invoice.objects.get(slug=slug)
    registration_fee = RegistrationFee.objects.get(student=invoice.student)
    groups = request.user.groups.all()
    if groups.exists():
        role = groups[0].name
    else:
        role = None  # or you can assign a default role, like 'guest'

    seats_booked = Booking.objects.filter(student=invoice.student)

    return render(
        request,
        "invoice_student_final.html",
        {
            "invoice": invoice,
            "registration_fee": registration_fee,
            "role": role,
            "seats": seats_booked,
            # "ads": ads,
        },
    )


@login_required(login_url="/librarian/login/")
def students_data(request):
    user = request.user
    role = user.groups.all()[0].name
    today = timezone.now().date()
    date_range_start = today - timedelta(days=10)
    date_range_end = today + timedelta(days=4)


    try:
        # Check if the user is a sublibrarian
        sublibrarian = Sublibrarian_param.objects.get(user=request.user)

        location = sublibrarian.librarian.librarian_address
        s1 = StudentData.objects.filter(sublibrarian=sublibrarian)
        s2 = StudentData.objects.filter(librarian=sublibrarian.librarian)
        students = s1 | s2
    except Sublibrarian_param.DoesNotExist:
        # If the user is not a sublibrarian, get the librarian's own address
        librarian = Librarian_param.objects.get(user=request.user)
        location = librarian.librarian_address
        students = StudentData.objects.filter(librarian=librarian)

    latest_invoices = Invoice.objects.filter(
        student__in=students,
        id__in=Invoice.objects.filter(student__in=students)
        .values("student")
        .annotate(max_id=Max("id"))
        .values("max_id"),
        is_active=True,
    )

    data = latest_invoices.filter(due_date__range=(date_range_start, date_range_end))

    for student in students:
        set_student_due_color(StudentData, student)
        student.save(update_fields=["color"])


    return render(
        request,
        "student_list.html",
        {"std": data, "role": role, "location": location},
    )


def send_students_email(request, slug):
    student = get_object_or_404(StudentData, slug=slug)

    # Get the latest invoice to determine due date and status
    latest_invoice = student.invoice_set.order_by("-due_date").first()

    if latest_invoice and latest_invoice.due_date:
        from django.utils import timezone
        from datetime import datetime

        today = timezone.now().date()
        due_date = latest_invoice.due_date

        # Ensure due_date is a date object
        if isinstance(due_date, str):
            try:
                due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
            except ValueError:
                due_date = today
        elif isinstance(due_date, datetime):
            due_date = due_date.date()

        delta = (due_date - today).days

        # Determine status and context based on due date
        if delta == 0:
            status = "due_today"
        elif -9 <= delta <= -1:
            status = "overdue"
            days_overdue = abs(delta)
        elif delta <= -10:
            status = "long_overdue"
            days_overdue = abs(delta)
        elif 1 <= delta <= 6:
            status = "upcoming"
            days_until_due = delta
        else:
            status = "general"
            days_until_due = delta if delta > 0 else 0

        # Prepare context for email template
        context = {
            "student": student,
            "due_date": due_date,
            "status": status,
            "library_name": student.librarian.library_name if student.librarian else "Librainian",
            "total_amount": latest_invoice.total_amount,
        }

        # Add status-specific context
        if status in ["overdue", "long_overdue"]:
            context["days_overdue"] = days_overdue
        elif status in ["upcoming", "general"]:
            context["days_until_due"] = days_until_due

        template_name = "fee_reminder_email.html"

        # Set dynamic subject based on status
        if status == "due_today":
            subject = f"Urgent: Library Fee Due Today - {student.librarian.library_name if student.librarian else 'Librainian'}"
        elif status == "overdue":
            subject = f"Important: Overdue Library Fee Payment - {student.librarian.library_name if student.librarian else 'Librainian'}"
        elif status == "long_overdue":
            subject = f"Final Notice: Long Overdue Library Fee - {student.librarian.library_name if student.librarian else 'Librainian'}"
        else:
            subject = f"Library Fee Payment Reminder - {student.librarian.library_name if student.librarian else 'Librainian'}"

    else:
        # Fallback to welcome email if no invoice found
        context = {
            "student": student,
            "library_name": student.librarian.library_name if student.librarian else "Librainian",
        }
        template_name = "Welcome_mail_student.html"
        subject = f"Welcome to {student.librarian.library_name if student.librarian else 'Librainian'}"

    try:
        send_dynamic_email(subject, template_name, context, student.email)
        messages.success(
            request,
            f"Fee reminder email sent successfully to {student.email}!",
        )
    except Exception as e:
        print(f"Email sending error: {str(e)}")
        messages.error(
            request,
            f"Failed to send email to {student.email}. Error: {str(e)}",
        )

    return redirect("students_data")


def send_student_sms(request, slug):
    student = get_object_or_404(StudentData, slug=slug)

    # Get the latest invoice to determine due date and status
    latest_invoice = student.invoice_set.order_by("-due_date").first()

    if latest_invoice and latest_invoice.due_date:
        from django.utils import timezone
        from datetime import datetime
        from utils.notifications import template_content

        today = timezone.now().date()
        due_date = latest_invoice.due_date

        # Ensure due_date is a date object
        if isinstance(due_date, str):
            try:
                due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
            except ValueError:
                due_date = today
        elif isinstance(due_date, datetime):
            due_date = due_date.date()

        delta = (due_date - today).days
        library_name = student.librarian.library_name if student.librarian else "Librainian"

        # Import the formatting function
        from utils.notifications import format_sms_message

        # Determine message template based on due date
        if delta == 0:
            # Due today
            template_key = "fee_due_today"
            message = format_sms_message(
                template_content[template_key]["content"],
                student.name,
                library_name,
                due_date.strftime("%d/%m/%Y"),
                student.unique_id
            )
            template_id = template_content[template_key]["template_id"]

        elif -9 <= delta <= -1:
            # Overdue
            days_overdue = abs(delta)
            template_key = "fee_overdue"
            message = format_sms_message(
                template_content[template_key]["content"],
                student.name,
                library_name,
                days_overdue,
                due_date.strftime("%d/%m/%Y"),
                student.unique_id
            )
            template_id = template_content[template_key]["template_id"]

        elif delta <= -10:
            # Long overdue
            days_overdue = abs(delta)
            template_key = "fee_long_overdue"
            message = format_sms_message(
                template_content[template_key]["content"],
                student.name,
                library_name,
                days_overdue,
                student.unique_id
            )
            template_id = template_content[template_key]["template_id"]

        elif 1 <= delta <= 6:
            # Upcoming due date
            template_key = "fee_reminder"
            message = format_sms_message(
                template_content[template_key]["content"],
                student.name,
                library_name,
                due_date.strftime("%d/%m/%Y"),
                delta,
                student.unique_id
            )
            template_id = template_content[template_key]["template_id"]

        else:
            # General reminder
            template_key = "library fee"
            message = format_sms_message(
                template_content[template_key]["content"],
                student.name
            )
            template_id = template_content[template_key]["template_id"]

    else:
        # Fallback message if no invoice found
        from utils.notifications import format_sms_message
        template_key = "library fee"
        message = format_sms_message(
            template_content[template_key]["content"],
            student.name
        )
        template_id = template_content[template_key]["template_id"]

    sender = "LBRIAN"
    number = student.mobile

    try:
        print(f"Sending SMS to {number}: {message}")
        response = send_sms(str(sender), str(number), str(message), str(template_id))
        print(f"SMS API Response: {response}")

        messages.success(
            request,
            f"Fee reminder SMS sent successfully to {student.name}!",
        )
    except Exception as e:
        print(f"SMS sending error: {str(e)}")
        messages.error(
            request,
            f"Failed to send SMS to {student.name}. Error: {str(e)}",
        )

    return redirect("students_data")


@login_required(login_url="/librarian/login/")
def student_data(request, slug):
    student = get_object_or_404(StudentData, slug=slug)
    invoice = Invoice.objects.filter(student=student).last()
    booking_seats = Booking.objects.filter(student=student)
    recent_invoices = Invoice.objects.filter(student=student)
    booking_seats = Booking.objects.filter(student=student)
    recent_invoices = Invoice.objects.filter(student=student)
    role = request.user.groups.all()[0].name

    try:
        student_registrations_fee = RegistrationFee.objects.get(student=student)
    except RegistrationFee.DoesNotExist:
        student_registrations_fee = None

    return render(
        request,
        "student_profile.html",
        {
            "std": student,
            "invoice": invoice,
            "booking_seats": booking_seats,
            "booking_seats": booking_seats,
            "recent_invoices": recent_invoices,
            "fee_status": student_registrations_fee,
            "role": role,
        },
    )


@login_required(login_url="/librarian/login/")
def student_update(request, slug):
    student = get_object_or_404(StudentData, slug=slug)
    states = States.objects.all()
    courses = Courses.objects.all()
    role = request.user.groups.all()[0].name

    return render(
        request,
        "student_update.html",
        {"std": student, "courses": courses, "states": states, "role": role},
    )


@login_required(login_url="/librarian/login/")
def student_doupdate(request, slug):
    if request.method == "POST":
        try:
            course_id = request.POST.get("course")
            name = request.POST.get("name", "").capitalize()
            fname = request.POST.get("fathername", "").capitalize()
            age = request.POST.get("age")
            gender = request.POST.get("gender")
            email = request.POST.get("email")
            mobile = request.POST.get("phone")
            locality = request.POST.get("locality")
            city = request.POST.get("city")
            state_id = request.POST.get("state")
            registration_fee = request.POST.get("regfees")
            image = request.FILES.get("image")

            student = get_object_or_404(StudentData, slug=slug)
            path = student.image.path if student.image else None

            course = Courses.objects.get(id=course_id)
            state = States.objects.get(id=state_id)

            age = int(age) if age else None
            fname = fname if fname else None

            # Get current librarian
            userid = request.user.id
            try:
                sublibrarian = Sublibrarian_param.objects.get(user=userid)
                current_librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                current_librarian = Librarian_param.objects.get(user=userid)

            # Check for duplicate email (excluding current student)
            if email and email != student.email:
                if StudentData.objects.filter(
                    librarian=current_librarian,
                    email=email
                ).exclude(id=student.id).exists():
                    messages.error(request, "A student with this email already exists for this library.")
                    return redirect(f"/students/update/{slug}/")

            # Check for duplicate mobile (excluding current student)
            if mobile and int(mobile) != student.mobile:
                if StudentData.objects.filter(
                    librarian=current_librarian,
                    mobile=mobile
                ).exclude(id=student.id).exists():
                    messages.error(request, "A student with this mobile number already exists for this library.")
                    return redirect(f"/students/update/{slug}/")

            if image:
                if path and os.path.exists(path):
                    os.remove(path)
                student.image = image

            student.course = course
            student.name = name
            student.f_name = fname
            student.age = age
            student.gender = gender
            student.email = email
            student.mobile = mobile
            student.locality = locality
            student.city = city
            student.state = state
            student.registration_fee = registration_fee

            try:
                sublibrarian = Sublibrarian_param.objects.get(user=userid)
                student.sublibrarian = sublibrarian
                student.librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                librarian = Librarian_param.objects.get(user=userid)
                student.librarian = librarian
                student.sublibrarian = None

            student.save()
            messages.success(request, "Student data updated successfully")
            return redirect("/students/")

        except IntegrityError as e:
            error_message = str(e).lower()
            if "unique constraint" in error_message or "duplicate" in error_message:
                if "email" in error_message:
                    messages.error(request, "A student with this email already exists for this library.")
                elif "mobile" in error_message:
                    messages.error(request, "A student with this mobile number already exists for this library.")
                else:
                    messages.error(request, "A student with this information already exists for this library.")
            else:
                messages.error(request, "Database error occurred. Please try again.")
            return redirect(f"/students/update/{slug}/")

        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect(f"/students/update/{slug}/")

    return HttpResponse("Invalid request method", status=400)


@login_required(login_url="/librarian/login/")
def student_delete(request, slug):
    student = get_object_or_404(StudentData, slug=slug)
    path = student.image.path
    os.remove(path)
    student.delete()
    return redirect("/students/")


def temp_student_form(request, slug):
    librarian = get_object_or_404(Librarian_param, slug=slug)
    states = States.objects.all()
    courses = Courses.objects.all()

    if request.method == "POST":
        try:
            # Get form data
            name = request.POST.get("name", "").strip()
            f_name = request.POST.get("f_name", "").strip()
            age = request.POST.get("age", "").strip()
            gender = request.POST.get("gender", "").strip()
            email = request.POST.get("email", "").strip()
            mobile = request.POST.get("mobile", "").strip()
            locality = request.POST.get("locality", "").strip()
            city = request.POST.get("city", "").strip()
            state_id = request.POST.get("state", "").strip()
            course_id = request.POST.get("course", "").strip()
            image = request.FILES.get("image")

            # Proof of identity fields
            identity_type = request.POST.get("identity_type", "").strip()
            identity_number = request.POST.get("identity_number", "").strip()

            # Validate required fields
            if not name:
                messages.error(request, "Name is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not gender:
                messages.error(request, "Gender is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not email:
                messages.error(request, "Email is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not mobile:
                messages.error(request, "Mobile number is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not locality:
                messages.error(request, "Locality is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not city:
                messages.error(request, "City is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not state_id:
                messages.error(request, "State is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if not course_id:
                messages.error(request, "Course is required.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            # Validate email format
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                messages.error(request, "Please enter a valid email address.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            # Validate mobile number (10 digits)
            if not mobile.isdigit() or len(mobile) != 10:
                messages.error(request, "Please enter a valid 10-digit mobile number.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            # Process age
            if age == "" or age == "0":
                age = None
            else:
                try:
                    age = int(age)
                    if age < 0 or age > 150:
                        messages.error(request, "Please enter a valid age between 0 and 150.")
                        return render(request, "temp_registration_form.html", {
                            "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                        })
                except ValueError:
                    messages.error(request, "Please enter a valid age.")
                    return render(request, "temp_registration_form.html", {
                        "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                    })

            # Get state and course objects with error handling
            try:
                state = States.objects.get(id=state_id)
            except States.DoesNotExist:
                messages.error(request, "Selected state is invalid.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })
            except ValueError:
                messages.error(request, "Invalid state selection.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            try:
                course = Courses.objects.get(id=course_id)
            except Courses.DoesNotExist:
                messages.error(request, "Selected course is invalid.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })
            except ValueError:
                messages.error(request, "Invalid course selection.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            # Validate identity number format and uniqueness if provided
            if identity_number and identity_number.strip():
                identity_number = identity_number.strip().upper()

                # Validate format based on identity type
                if identity_type:
                    import re
                    valid_format = False

                    if identity_type == 'aadhaar':
                        valid_format = re.match(r'^\d{12}$', identity_number)
                        if not valid_format:
                            messages.error(request, "Aadhaar number must be exactly 12 digits.")
                    elif identity_type == 'pan':
                        valid_format = re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', identity_number)
                        if not valid_format:
                            messages.error(request, "PAN must be in format: 5 letters, 4 digits, 1 letter (e.g., **********).")
                    elif identity_type == 'driving_license':
                        valid_format = re.match(r'^[A-Z0-9]{15}$', identity_number)
                        if not valid_format:
                            messages.error(request, "Driving License must be exactly 15 alphanumeric characters.")
                    elif identity_type == 'voter_id':
                        valid_format = re.match(r'^[A-Z0-9]{10}$', identity_number)
                        if not valid_format:
                            messages.error(request, "Voter ID must be exactly 10 alphanumeric characters.")

                    if not valid_format:
                        return render(request, "temp_registration_form.html", {
                            "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                        })

                # Check uniqueness
                if StudentData.objects.filter(identity_number=identity_number).exists():
                    messages.error(request, "A student with this identity number already exists.")
                    return render(request, "temp_registration_form.html", {
                        "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                    })
                if TempStudentData.objects.filter(identity_number=identity_number).exists():
                    messages.error(request, "A student with this identity number is already in the registration process.")
                    return render(request, "temp_registration_form.html", {
                        "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                    })

            # Check for duplicate email or mobile
            if TempStudentData.objects.filter(librarian=librarian, email=email).exists():
                messages.error(request, "A student with this email already exists for this library.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            if TempStudentData.objects.filter(librarian=librarian, mobile=mobile).exists():
                messages.error(request, "A student with this mobile number already exists for this library.")
                return render(request, "temp_registration_form.html", {
                    "slug": slug, "states": states, "courses": courses, "librarian": librarian,
                })

            # Create student record
            student = TempStudentData(
                librarian=librarian,
                name=name,
                f_name=f_name if f_name else None,
                age=age,
                gender=gender,
                email=email,
                mobile=mobile,
                locality=locality,
                city=city,
                state=state,
                course=course,
                image=image,
                identity_type=identity_type if identity_type else None,
                identity_number=identity_number if identity_number else None,
            )

            student.save()

            # Send email notification
            try:
                subject = f"New Admission Submitted by {name}"
                template_name = "student_registration_email.html"
                context = {"student": student}
                recipient_list = librarian.user.email

                send_dynamic_email(subject, template_name, context, recipient_list)
            except Exception as e:
                # Log the email error but don't fail the registration
                print(f"Email sending failed: {str(e)}")

            messages.success(request, "Student registered successfully!")
            # Redirect to feedback form instead of success page
            return redirect(f"/students/registration-feedback/{student.id}/{slug}/")

        except IntegrityError as e:
            error_message = str(e).lower()
            if "unique constraint" in error_message or "duplicate" in error_message:
                if "email" in error_message:
                    messages.error(request, "A student with this email already exists.")
                elif "mobile" in error_message:
                    messages.error(request, "A student with this mobile number already exists.")
                else:
                    messages.error(request, "A student with this information already exists.")
            else:
                messages.error(request, "Database error occurred. Please try again.")

            return render(request, "temp_registration_form.html", {
                "slug": slug, "states": states, "courses": courses, "librarian": librarian,
            })

        except Exception as e:
            # Log the specific error for debugging
            print(f"Error in temp_student_form: {str(e)}")
            messages.error(request, f"An unexpected error occurred: {str(e)}")
            return render(request, "temp_registration_form.html", {
                "slug": slug, "states": states, "courses": courses, "librarian": librarian,
            })

    # GET request - show the form
    return render(
        request,
        "temp_registration_form.html",
        {
            "slug": slug,
            "states": states,
            "courses": courses,
            "librarian": librarian,
        },
    )

@login_required(login_url="/librarian/login/")
@membership_required("Basic")
def temp_students_list(request):
    user = request.user
    librarian = Librarian_param.objects.get(user=user)
    temp_students = TempStudentData.objects.filter(librarian=librarian)
    role = request.user.groups.all()[0].name
    if request.method == "POST":
        student_id = request.POST.get("student_id")
        status = request.POST.get("status")
        temp_student = get_object_or_404(TempStudentData, id=student_id)

        if status == "completed":
            try:
                if not StudentData.objects.filter(
                    librarian=temp_student.librarian, email=temp_student.email, mobile=temp_student.mobile
                ).exists():

                    student = StudentData(
                        librarian=temp_student.librarian,
                        name=temp_student.name,
                        f_name=temp_student.f_name,
                        age=temp_student.age,
                        gender=temp_student.gender,
                        email=temp_student.email,
                        mobile=temp_student.mobile,
                        locality=temp_student.locality,
                        city=temp_student.city,
                        state=temp_student.state,
                        course=temp_student.course,
                        image=temp_student.image,
                        registration_fee=0,
                        registration_date=timezone.now(),
                    )
                    student.save()
                    RegistrationFee.objects.create(student=student, is_paid=True)
                    temp_student.delete()
                    return redirect(f"/students/create_invoice/{student.slug}/")
                else:
                    return render(request, "temp_students_list.html",{"status": "error", "message": "Record already exists"})
            except  IntegrityError:
                return render(request, "temp_students_list.html", {"status": "error", "message": "Duplicate record, integrity error."})

    return render(
        request,
        "temp_students_list.html",
        {"temp_students": temp_students, "role": role},
    )

def temp_subLib_students_list(request):
    user = request.user
    librarian = Sublibrarian_param.objects.get(user=user)
    temp_students = TempStudentData.objects.filter(librarian=librarian.librarian)
    role = request.user.groups.all()[0].name
    if request.method == "POST":
        student_id = request.POST.get("student_id")
        status = request.POST.get("status")
        temp_student = get_object_or_404(TempStudentData, id=student_id)

        if status == "completed":
            student = StudentData(
                sublibrarian=librarian,
                name=temp_student.name,
                f_name=temp_student.f_name,
                age=temp_student.age,
                gender=temp_student.gender,
                email=temp_student.email,
                mobile=temp_student.mobile,
                locality=temp_student.locality,
                city=temp_student.city,
                state=temp_student.state,
                course=temp_student.course,
                image=temp_student.image,
                registration_fee=0,
                registration_date=timezone.now(),
            )
            student.save()
            RegistrationFee.objects.create(student=student, is_paid=True)
            temp_student.delete()
            return redirect(f"/students/create_invoice/{student.slug}/")

    return render(
        request,
        "temp_students_list.html",
        {"temp_students": temp_students, "role": role},
    )



def page_not_found_view(request, exception=None):
    return render(request, "404.html", status=404)


def student_approval(request, pk):
    if request.method == "GET":
        status = ""
        try:
            print(pk)
            library = Invoice.objects.get(id=pk)
            library.is_active = False if library.is_active  else True
            library.save()
            status = "student approved successfully"
        except Librarian_param.DoesNotExist:
            status = "student does not exist"
        print(status)
        return HttpResponse({"status": status},status=200)

@login_required(login_url="/librarian/login/")
def update_temp_student(request, student_id):
    temp_student = get_object_or_404(TempStudentData, id=student_id)
    user = request.user
    librarian = Librarian_param.objects.get(user=user)

    if request.method == "POST":
        if temp_student.librarian != librarian:
            messages.error(request, "Unauthorized access.")
            return redirect("temp_students_list")

        temp_student.name = request.POST.get("name", temp_student.name)
        temp_student.f_name = request.POST.get("f_name", temp_student.f_name)
        temp_student.age = request.POST.get("age", temp_student.age)
        temp_student.gender = request.POST.get("gender", temp_student.gender)
        temp_student.email = request.POST.get("email", temp_student.email)
        temp_student.mobile = request.POST.get("mobile", temp_student.mobile)
        temp_student.locality = request.POST.get("locality", temp_student.locality)
        # temp_student.city = request.POST.get("city", temp_student.city)
        # temp_student.course = request.POST.get("course", temp_student.course)

        temp_student.save()
        messages.success(request, "Student updated successfully.")
        return redirect("temp_students_list")

    return render(request, "update_temp_student.html", {"temp_student": temp_student})

@login_required(login_url="/librarian/login/")
def delete_temp_student(request, student_id):
    temp_student = get_object_or_404(TempStudentData, id=student_id)
    user = request.user
    librarian = Librarian_param.objects.get(user=user)

    if temp_student.librarian != librarian:
        messages.error(request, "Unauthorized access.")
    else:
        temp_student.delete()
        messages.success(request, "Student deleted successfully.")

    return redirect("temp_students_list")


def registration_feedback_form(request, student_id, slug):
    """Handle registration feedback form display and submission"""
    try:
        # Get the temporary student and librarian
        temp_student = get_object_or_404(TempStudentData, id=student_id)
        librarian = get_object_or_404(Librarian_param, slug=slug)

        # Verify that the student belongs to this librarian
        if temp_student.librarian != librarian:
            messages.error(request, "Invalid access to feedback form.")
            return redirect('/')

        if request.method == 'POST':
            try:
                # Get form data
                ease_rating = request.POST.get('ease_rating')
                time_taken = request.POST.get('time_taken')
                faced_issues = request.POST.get('faced_issues') == 'true'
                issue_description = request.POST.get('issue_description', '').strip()
                design_rating = request.POST.get('design_rating')
                device_used = request.POST.get('device_used')
                qr_easy_to_scan = request.POST.get('qr_easy_to_scan') == 'true'
                qr_problem_description = request.POST.get('qr_problem_description', '').strip()
                suggestions = request.POST.get('suggestions', '').strip()
                would_recommend = request.POST.get('would_recommend') == 'true'

                # Validate required fields
                if not all([ease_rating, time_taken, design_rating, device_used]):
                    messages.error(request, "Please fill in all required fields.")
                    return render(request, 'registration_feedback_form.html', {
                        'temp_student': temp_student,
                        'librarian': librarian,
                        'slug': slug
                    })

                # Validate numeric fields
                try:
                    ease_rating = int(ease_rating)
                    design_rating = int(design_rating)

                    if not (0 <= ease_rating <= 10):
                        raise ValueError("Ease rating must be between 0 and 10")
                    if not (1 <= design_rating <= 5):
                        raise ValueError("Design rating must be between 1 and 5")

                except ValueError as e:
                    messages.error(request, f"Invalid rating value: {str(e)}")
                    return render(request, 'registration_feedback_form.html', {
                        'temp_student': temp_student,
                        'librarian': librarian,
                        'slug': slug
                    })

                # Validate conditional fields
                if faced_issues and not issue_description:
                    messages.error(request, "Please describe the issues you faced.")
                    return render(request, 'registration_feedback_form.html', {
                        'temp_student': temp_student,
                        'librarian': librarian,
                        'slug': slug
                    })

                if not qr_easy_to_scan and not qr_problem_description:
                    messages.error(request, "Please describe the QR code problem.")
                    return render(request, 'registration_feedback_form.html', {
                        'temp_student': temp_student,
                        'librarian': librarian,
                        'slug': slug
                    })

                # Get client IP and user agent
                def get_client_ip(request):
                    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
                    if x_forwarded_for:
                        ip = x_forwarded_for.split(',')[0]
                    else:
                        ip = request.META.get('REMOTE_ADDR')
                    return ip

                ip_address = get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')

                # Create feedback record
                feedback = RegistrationFeedback.objects.create(
                    temp_student=temp_student,
                    librarian=librarian,
                    ease_rating=ease_rating,
                    time_taken=time_taken,
                    faced_issues=faced_issues,
                    issue_description=issue_description if faced_issues else None,
                    design_rating=design_rating,
                    device_used=device_used,
                    qr_easy_to_scan=qr_easy_to_scan,
                    qr_problem_description=qr_problem_description if not qr_easy_to_scan else None,
                    suggestions=suggestions if suggestions else None,
                    would_recommend=would_recommend,
                    ip_address=ip_address,
                    user_agent=user_agent
                )

                # Send feedback email to office
                try:
                    subject = f"New Registration Feedback - {librarian.library_name}"
                    template_name = "registration_feedback_email.html"
                    context = {
                        "feedback": feedback,
                        "temp_student": temp_student,
                        "librarian": librarian
                    }

                    # Send to office email (configurable via admin)
                    office_email = "<EMAIL>"  # This can be made configurable
                    send_dynamic_email(subject, template_name, context, office_email)
                    print(f"✅ Feedback email sent successfully to {office_email}")

                except Exception as e:
                    # Log email error but don't fail the feedback submission
                    print(f"❌ Feedback email sending failed: {str(e)}")
                    print("✅ Feedback was still saved successfully to database.")

                messages.success(request, "Thank you for your feedback! Your response has been recorded.")
                return render(request, 'feedback_success.html', {
                    'temp_student': temp_student,
                    'librarian': librarian,
                    'feedback': feedback
                })

            except Exception as e:
                print(f"Error processing feedback: {str(e)}")
                messages.error(request, "An error occurred while processing your feedback. Please try again.")
                return render(request, 'registration_feedback_form.html', {
                    'temp_student': temp_student,
                    'librarian': librarian,
                    'slug': slug
                })

        # GET request - show the feedback form
        return render(request, 'registration_feedback_form.html', {
            'temp_student': temp_student,
            'librarian': librarian,
            'slug': slug
        })

    except Exception as e:
        print(f"Error in registration_feedback_form: {str(e)}")
        messages.error(request, "An error occurred. Please try again.")
        return redirect('/')


