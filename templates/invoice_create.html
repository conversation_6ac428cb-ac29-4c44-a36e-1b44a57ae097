{% extends "base.html" %}
{% load math_filters %}

{% block title %}Create Invoice - {{ student.name }} - Librainian{% endblock %}

{% block page_title %}Create Invoice{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="/{{ role }}/students/">Students</a></li>
<li class="breadcrumb-item active" aria-current="page">Create Invoice</li>
{% endblock %}

{% block extra_css %}
<style>
    /* Invoice Creation Styles */
    .invoice-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
    }

    .invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .invoice-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .invoice-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .invoice-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .student-info-card {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .student-info-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .student-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .student-info-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .info-label {
        font-size: 0.875rem;
        opacity: 0.8;
        margin-bottom: 0.25rem;
        font-weight: 500;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
    }

    .form-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid rgba(99, 102, 241, 0.2);
    }

    .section-title i {
        color: #6366f1;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .required-label::after {
        content: " *";
        color: #ef4444;
        font-size: 0.875rem;
        font-weight: 700;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .form-control:read-only {
        background: rgba(243, 244, 246, 0.8);
        color: #6b7280;
        cursor: not-allowed;
    }

    .form-select {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
        cursor: pointer;
    }

    .form-select:focus {
        outline: none;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        background: rgba(255, 255, 255, 1);
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-group-text {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6b7280;
        font-size: 1rem;
        z-index: 2;
        background: none;
        border: none;
        pointer-events: none;
    }

    .input-group .form-control,
    .input-group .form-select {
        padding-left: 2.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .calculation-card {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border-radius: 16px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 2rem;
        text-align: center;
    }

    .calculation-title {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .calculation-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .calculation-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .calc-label {
        font-size: 0.875rem;
        opacity: 0.8;
        margin-bottom: 0.25rem;
    }

    .calc-value {
        font-size: 1.25rem;
        font-weight: 700;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        border: none;
        border-radius: 12px;
        padding: 0.875rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
    }

    .btn-secondary:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(156, 163, 175, 0.2);
    }

    .help-text {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        display: block;
        font-size: 0.875rem;
        color: #ef4444;
        margin-top: 0.25rem;
    }

    /* Partial Payment Styles */
    .payment-type-selector {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .payment-option {
        position: relative;
    }

    .payment-option input[type="radio"] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .payment-option-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1.5rem 1rem;
        border: 2px solid rgba(99, 102, 241, 0.2);
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .payment-option-label:hover {
        border-color: rgba(99, 102, 241, 0.4);
        background: rgba(99, 102, 241, 0.05);
        transform: translateY(-2px);
    }

    .payment-option input[type="radio"]:checked + .payment-option-label {
        border-color: #6366f1;
        background: rgba(99, 102, 241, 0.1);
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
    }

    .payment-option-label i {
        font-size: 2rem;
        color: #6366f1;
        margin-bottom: 0.5rem;
    }

    .option-title {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .option-desc {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .partial-payment-section {
        background: rgba(99, 102, 241, 0.05);
        border: 1px solid rgba(99, 102, 241, 0.2);
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .payment-summary-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1rem;
        border: 1px solid rgba(99, 102, 241, 0.2);
    }

    .payment-summary-card h6 {
        color: #1f2937;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .summary-row:last-of-type {
        border-bottom: none;
    }

    .summary-row.highlight {
        font-weight: 600;
        color: #059669;
        font-size: 1.1rem;
    }

    .progress-container {
        margin-top: 1rem;
    }

    .progress {
        height: 8px;
        background-color: rgba(99, 102, 241, 0.1);
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-bar {
        background: linear-gradient(90deg, #6366f1, #8b5cf6);
        height: 100%;
        transition: width 0.3s ease;
    }

    .remaining-amount {
        margin-top: 0.5rem;
        padding: 0.75rem;
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        border-radius: 8px;
        color: #dc2626;
        font-weight: 500;
        display: none;
    }

    .remaining-amount.show {
        display: block;
    }

    .remaining-amount.valid {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.2);
        color: #059669;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .invoice-container,
        .form-section {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .invoice-header {
            padding: 1.5rem 1rem;
        }

        .invoice-header h1 {
            font-size: 1.5rem;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .form-actions {
            flex-direction: column-reverse;
            gap: 1rem;
        }

        .btn-primary,
        .btn-secondary {
            width: 100%;
            justify-content: center;
        }

        .student-info-grid,
        .calculation-grid {
            grid-template-columns: 1fr;
        }

        .shifts-grid,
        .months-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .shift-option,
        .month-option {
            padding: 1rem;
            min-height: 70px;
        }
    }

    @media (max-width: 480px) {
        .shifts-grid,
        .months-grid {
            gap: 0.75rem;
        }

        .shift-option,
        .month-option {
            padding: 0.875rem;
            border-radius: 12px;
        }

        .option-title {
            font-size: 0.9rem;
        }

        .option-price {
            font-size: 0.8rem;
        }
    }

    /* Selection Cards Styling */
    .shifts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.25rem;
        margin-bottom: 1rem;
    }

    .shift-option {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 16px;
        padding: 1.25rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        min-height: 80px;
        display: flex;
        align-items: center;
    }

    .shift-option:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: rgba(99, 102, 241, 0.5);
    }

    .shift-option.selected {
        border-color: #6366f1;
        background: rgba(99, 102, 241, 0.1);
        box-shadow: 0 4px 20px rgba(99, 102, 241, 0.2);
    }

    /* Billing Info Display */
    .billing-info-display {
        background: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .current-cycle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #3b82f6;
        font-weight: 600;
    }

    .cycle-description {
        color: #6b7280;
        font-weight: 400;
        font-size: 0.875rem;
    }

    /* Half-Cycle Options */
    .half-cycle-options {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .half-cycle-options .form-check {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(156, 163, 175, 0.3);
        border-radius: 12px;
        padding: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
        margin: 0;
    }

    .half-cycle-options .form-check:hover {
        border-color: rgba(99, 102, 241, 0.5);
        transform: translateY(-1px);
    }

    .half-cycle-options .form-check-input:checked + .form-check-label {
        color: #6366f1;
    }

    .half-cycle-options .form-check-input:checked ~ * {
        border-color: #6366f1;
        background: rgba(99, 102, 241, 0.1);
    }

    .option-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .option-description {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .shift-option::before,
    .month-option::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .shift-option.selected::before,
    .month-option.selected::before {
        opacity: 1;
    }

    .form-check {
        width: 100%;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
        border: 2px solid #d1d5db;
        border-radius: 6px;
        background: white;
        transition: all 0.3s ease;
        cursor: pointer;
        margin: 0;
        flex-shrink: 0;
    }

    .form-check-input:checked {
        background: #6366f1;
        border-color: #6366f1;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }

    .form-check-input:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }

    .form-check-label {
        cursor: pointer;
        width: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .option-title {
        font-weight: 600;
        color: #1f2937;
        font-size: 1rem;
        line-height: 1.4;
    }

    .option-price {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Animation */
    .fade-in {
        animation: fadeIn 0.6s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="invoice-container fade-in">
    <!-- Student Information Card -->
    <div class="student-info-card">
        <div class="student-info-title">
            <i class="fas fa-user-graduate"></i>
            Student Information
        </div>
        <div class="student-info-grid">
            <div class="student-info-item">
                <div class="info-label">Full Name</div>
                <div class="info-value">{{ student.name }}</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">Course</div>
                <div class="info-value">{{ student.course }}</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">Age</div>
                <div class="info-value">{{ student.age }} years</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">Gender</div>
                <div class="info-value">{{ student.gender }}</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">Email</div>
                <div class="info-value">{{ student.email }}</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">Mobile</div>
                <div class="info-value">{{ student.mobile }}</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">City</div>
                <div class="info-value">{{ student.city }}, {{ student.state }}</div>
            </div>
            <div class="student-info-item">
                <div class="info-label">Locality</div>
                <div class="info-value">{{ student.locality }}</div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    {% endif %}

    <form method="POST" id="invoiceForm" novalidate>
        {% csrf_token %}

        <!-- Invoice Details Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>
                Invoice Details
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="due_date" class="form-label required-label">Payment date</label>
                    <div class="input-group">
                        <i class="fas fa-calendar input-group-text"></i>
                        <input type="date" name="due_date" id="due_date" class="form-control"
                               value="{{ invoice.due_date }}" required>
                    </div>
                    <div class="help-text">Select the payment date</div>
                </div>

                <div class="form-group">
                    <label for="discount_amount" class="form-label">Discount Amount</label>
                    <div class="input-group">
                        <i class="fas fa-percent input-group-text"></i>
                        <input type="number" name="discount_amount" id="discount_amount" class="form-control"
                               min="0" step="0.01" placeholder="0.00">
                    </div>
                    <div class="help-text">Enter discount amount (optional)</div>
                </div>
            </div>

            <div class="form-group">
                <label for="mode_pay" class="form-label required-label">Payment Mode</label>
                <div class="input-group">
                    <i class="fas fa-credit-card input-group-text"></i>
                    <select class="form-select" id="mode_pay" name="mode_pay" required>
                        <option value="">Select payment mode</option>
                        <option value="cash">Cash</option>
                        <option value="upi">UPI</option>
                        <option value="card">Card</option>
                        <option value="bank transfer">Bank Transfer</option>
                        <option value="cheque">Cheque</option>
                    </select>
                </div>
                <div class="help-text">Choose the preferred payment method</div>
            </div>
        </div>

        <!-- Billing Period Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>
                Billing Period
            </div>

            <div class="form-group">
                <label class="form-label">Billing Cycle</label>
                <div class="billing-info-display">
                    <div class="current-cycle">
                        <i class="fas fa-clock"></i>
                        <strong>{{ student.librarian.get_default_billing_cycle_display|title }}</strong>
                        <span class="cycle-description">
                            {% if student.librarian.default_billing_cycle == 'daily' %}
                                (Pay per day)
                            {% elif student.librarian.default_billing_cycle == 'weekly' %}
                                (Pay per week)
                            {% else %}
                                (Pay per month)
                            {% endif %}
                        </span>
                    </div>
                </div>
                <input type="hidden" name="billing_cycle" value="{{ student.librarian.default_billing_cycle|default:'monthly' }}">
            </div>

            {% if student.librarian.allow_half_cycle_billing and student.librarian.default_billing_cycle != 'daily' %}
            <div class="form-group">
                <label class="form-label">Half-Cycle Option</label>
                <div class="half-cycle-options">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" id="full_cycle" name="cycle_type" value="full" checked>
                        <label class="form-check-label" for="full_cycle">
                            <div class="option-title">Full {{ student.librarian.get_default_billing_cycle_display|title }}</div>
                            <div class="option-description">Standard rate</div>
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" id="half_cycle" name="cycle_type" value="half">
                        <label class="form-check-label" for="half_cycle">
                            <div class="option-title">Half {{ student.librarian.get_default_billing_cycle_display|title }}</div>
                            <div class="option-description">
                                {% if student.librarian.default_billing_cycle == 'weekly' %}
                                    Every 3-4 days (+10% premium)
                                {% else %}
                                    Every 15 days (+10% premium)
                                {% endif %}
                            </div>
                        </label>
                    </div>
                </div>
            </div>
            {% else %}
            <input type="hidden" name="cycle_type" value="full">
            {% endif %}

            <div class="form-group">
                <label for="billing_units" class="form-label required-label">Number of Periods</label>
                <div class="input-group">
                    <i class="fas fa-hashtag input-group-text"></i>
                    <input type="number" name="billing_units" id="billing_units" class="form-control"
                           value="1" min="1" max="12" required>
                    <span class="input-group-text" id="billing-period-text">
                        {% if student.librarian.default_billing_cycle == 'daily' %}
                            day(s)
                        {% elif student.librarian.default_billing_cycle == 'weekly' %}
                            week(s)
                        {% else %}
                            month(s)
                        {% endif %}
                    </span>
                </div>
                <div class="help-text">Number of billing periods to include in this invoice</div>
            </div>
        </div>

        <!-- Shifts Selection Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-clock"></i>
                Select Shifts
            </div>

            <div class="form-group">
                <label class="form-label required-label">Available Shifts</label>
                <div class="shifts-grid">
                    {% for shift in shifts %}
                    <div class="shift-option">
                        <div class="form-check">
                            <input class="form-check-input shift-checkbox" type="checkbox" id="shift{{ shift.id }}"
                                   name="shifts" value="{{ shift.id }}" data-price="{{ shift.price }}">
                            <label class="form-check-label" for="shift{{ shift.id }}">
                                <div class="option-title">{{ shift.name }}</div>
                                <div class="option-price">
                                    ₹{{ shift.price }} per {{ student.librarian.get_default_billing_cycle_display|default:'month' }}
                                </div>
                            </label>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="help-text">Select one or more shifts for the invoice</div>
            </div>
        </div>




        <!-- Partial Payment Section -->
        <div class="form-section">
            <div class="section-title">
                <i class="fas fa-money-bill-wave"></i>
                Payment Options
            </div>

            <div class="form-group">
                <div class="payment-type-selector">
                    <div class="payment-option">
                        <input type="radio" id="full_payment" name="payment_type" value="full" checked>
                        <label for="full_payment" class="payment-option-label">
                            <i class="fas fa-check-circle"></i>
                            <span class="option-title">Full Payment</span>
                            <span class="option-desc">Pay the complete amount now</span>
                        </label>
                    </div>
                    <div class="payment-option">
                        <input type="radio" id="partial_payment" name="payment_type" value="partial">
                        <label for="partial_payment" class="payment-option-label">
                            <i class="fas fa-coins"></i>
                            <span class="option-title">Partial Payment</span>
                            <span class="option-desc">Pay a portion now, rest later</span>
                        </label>
                    </div>
                </div>
            </div>

            <div id="partial_payment_details" class="partial-payment-section" style="display: none;">
                <div class="form-row">
                    <div class="form-group">
                        <label for="partial_amount" class="form-label required-label">💳 Amount Paid Now</label>
                        <div class="input-group">
                            <i class="fas fa-rupee-sign input-group-text"></i>
                            <input type="number" name="partial_amount" id="partial_amount" class="form-control"
                                   min="1" step="1" placeholder="Enter amount">
                        </div>
                        <div class="help-text">Amount being paid today</div>
                        <div id="remaining_amount_display" class="remaining-amount"></div>
                    </div>

                    <div class="form-group">
                        <label for="next_payment_date" class="form-label">📅 Next Payment Date (Optional)</label>
                        <div class="input-group">
                            <i class="fas fa-calendar input-group-text"></i>
                            <input type="date" name="next_payment_date" id="next_payment_date" class="form-control">
                        </div>
                        <div class="help-text">Expected date of next payment (optional)</div>
                    </div>
                </div>

                <div class="payment-summary-card">
                    <h6><i class="fas fa-calculator"></i> Payment Summary</h6>
                    <div class="summary-row">
                        <span>Total Amount:</span>
                        <span id="summary_total">₹0</span>
                    </div>
                    <div class="summary-row">
                        <span>Paying Now:</span>
                        <span id="summary_paying">₹0</span>
                    </div>
                    <div class="summary-row highlight">
                        <span>Remaining Balance:</span>
                        <span id="summary_remaining">₹0</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" id="payment_progress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small id="progress_text">0% paid</small>
                    </div>
                </div>
            </div>

            <input type="hidden" name="is_partial_payment" id="is_partial_payment" value="off">

            <!-- Debug button - remove this after testing -->
            <button type="button" onclick="window.invoiceManager.togglePartialPayment()" style="background: red; color: white; padding: 10px; margin: 10px;">
                DEBUG: Toggle Partial Payment
            </button>
        </div>



        <!-- Calculation Summary -->
        <div class="calculation-card" id="calculationCard" style="display: none;">
            <div class="calculation-title">
                <i class="fas fa-calculator me-2"></i>
                Invoice Summary
            </div>
            <div class="calculation-grid">
                <div class="calculation-item">
                    <div class="calc-label">Subtotal</div>
                    <div class="calc-value" id="subtotal">₹0.00</div>
                </div>
                <div class="calculation-item">
                    <div class="calc-label">Discount</div>
                    <div class="calc-value" id="discount">₹0.00</div>
                </div>
                <div class="calculation-item">
                    <div class="calc-label">Total Amount</div>
                    <div class="calc-value" id="total">₹0.00</div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="/students/" class="btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Students
            </a>
            <button type="submit" class="btn-primary" id="submitBtn">
                <i class="fas fa-file-invoice"></i>
                Next Select Seat
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enhanced Invoice Creation System
    class InvoiceManager {
        constructor() {
            this.form = document.getElementById('invoiceForm');
            this.submitBtn = document.getElementById('submitBtn');
            this.shiftCheckboxes = document.querySelectorAll('.shift-checkbox');
            this.discountInput = document.getElementById('discount_amount');
            this.calculationCard = document.getElementById('calculationCard');

            // Billing elements
            this.billingUnitsInput = document.getElementById('billing_units');
            this.cycleTypeRadios = document.querySelectorAll('input[name="cycle_type"]');

            // Partial payment elements
            this.paymentTypeRadios = document.querySelectorAll('input[name="payment_type"]');
            this.partialPaymentSection = document.getElementById('partial_payment_details');
            this.partialAmountInput = document.getElementById('partial_amount');
            this.isPartialPaymentInput = document.getElementById('is_partial_payment');
            this.remainingAmountDisplay = document.getElementById('remaining_amount_display');

            // Summary elements
            this.summaryTotal = document.getElementById('summary_total');
            this.summaryPaying = document.getElementById('summary_paying');
            this.summaryRemaining = document.getElementById('summary_remaining');
            this.paymentProgress = document.getElementById('payment_progress');
            this.progressText = document.getElementById('progress_text');

            this.totalAmount = 0;
            this.init();

            // Make this instance globally accessible for debugging
            window.invoiceManager = this;
        }

        init() {
            this.setupFormValidation();
            this.setupFormSubmission();
            this.setupCalculations();
            this.setupCheckboxStyling();
            this.setupPartialPayments();
        }

        setupFormValidation() {
            const inputs = this.form.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearErrors(input));
            });
        }

        validateField(field) {
            const value = field.value.trim();
            let isValid = true;
            let errorMessage = '';

            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Date validation - Allow back dates for invoice creation
            if (field.type === 'date' && value) {
                const selectedDate = new Date(value);
                // Remove past date restriction to allow back dating
                // This is useful for creating invoices for previous periods
            }

            // Discount validation
            if (field.name === 'discount_amount' && value) {
                const discount = parseFloat(value);
                if (discount < 0) {
                    isValid = false;
                    errorMessage = 'Discount cannot be negative.';
                }
            }

            this.showFieldError(field, isValid ? '' : errorMessage);
            return isValid;
        }

        showFieldError(field, message) {
            const existingError = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            if (message) {
                field.classList.add('is-invalid');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.style.display = 'block';
                errorDiv.style.fontSize = '0.875rem';
                errorDiv.style.color = '#ef4444';
                errorDiv.style.marginTop = '0.25rem';
                errorDiv.textContent = message;
                field.parentNode.parentNode.appendChild(errorDiv);
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
        }

        clearErrors(field) {
            field.classList.remove('is-invalid', 'is-valid');
            const errorDiv = field.parentNode.parentNode.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        setupCalculations() {
            // Add event listeners for calculation updates
            this.shiftCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => this.updateCalculations());
            });

            this.billingUnitsInput.addEventListener('input', () => this.updateCalculations());

            if (this.cycleTypeRadios.length > 0) {
                this.cycleTypeRadios.forEach(radio => {
                    radio.addEventListener('change', () => this.updateCalculations());
                });
            }

            this.discountInput.addEventListener('input', () => this.updateCalculations());
        }

        setupPartialPayments() {
            // Debug: Check if elements are found
            console.log('Payment type radios found:', this.paymentTypeRadios.length);
            console.log('Partial payment section found:', !!this.partialPaymentSection);
            console.log('Partial amount input found:', !!this.partialAmountInput);

            // Payment type radio buttons
            this.paymentTypeRadios.forEach((radio, index) => {
                console.log(`Setting up radio ${index}:`, radio.value);
                radio.addEventListener('change', () => {
                    console.log('Radio changed to:', radio.value);
                    this.togglePartialPayment();
                });

                // Also add click event as backup
                radio.addEventListener('click', () => {
                    console.log('Radio clicked:', radio.value);
                    setTimeout(() => this.togglePartialPayment(), 10);
                });
            });

            // Also add event listeners to labels for better UX
            const labels = document.querySelectorAll('.payment-option-label');
            labels.forEach(label => {
                label.addEventListener('click', () => {
                    console.log('Label clicked');
                    setTimeout(() => this.togglePartialPayment(), 10);
                });
            });

            // Partial amount input
            if (this.partialAmountInput) {
                this.partialAmountInput.addEventListener('input', () => this.updatePartialPaymentSummary());
            }
        }

        togglePartialPayment() {
            const isPartial = document.querySelector('input[name="payment_type"]:checked').value === 'partial';
            console.log('Toggle partial payment called, isPartial:', isPartial);

            if (this.partialPaymentSection) {
                this.partialPaymentSection.style.display = isPartial ? 'block' : 'none';
                console.log('Partial payment section display set to:', isPartial ? 'block' : 'none');
            } else {
                console.log('Partial payment section not found!');
            }

            if (this.isPartialPaymentInput) {
                this.isPartialPaymentInput.value = isPartial ? 'on' : 'off';
                console.log('Hidden input value set to:', isPartial ? 'on' : 'off');
            } else {
                console.log('Hidden input not found!');
            }

            if (isPartial) {
                this.updatePartialPaymentSummary();
            }
        }

        updatePartialPaymentSummary() {
            const partialAmount = parseFloat(this.partialAmountInput.value) || 0;
            const remaining = this.totalAmount - partialAmount;
            const percentage = this.totalAmount > 0 ? (partialAmount / this.totalAmount) * 100 : 0;

            // Update summary
            if (this.summaryTotal) this.summaryTotal.textContent = `₹${this.totalAmount}`;
            if (this.summaryPaying) this.summaryPaying.textContent = `₹${partialAmount}`;
            if (this.summaryRemaining) this.summaryRemaining.textContent = `₹${remaining}`;

            // Update progress bar
            if (this.paymentProgress) {
                this.paymentProgress.style.width = `${Math.min(percentage, 100)}%`;
            }
            if (this.progressText) {
                this.progressText.textContent = `${Math.round(percentage)}% paid`;
            }

            // Validation feedback
            if (this.remainingAmountDisplay) {
                if (partialAmount > this.totalAmount) {
                    this.remainingAmountDisplay.textContent = `⚠️ Amount exceeds total (₹${this.totalAmount})`;
                    this.remainingAmountDisplay.className = 'remaining-amount show';
                } else if (partialAmount > 0 && partialAmount <= this.totalAmount) {
                    this.remainingAmountDisplay.textContent = `✅ Remaining balance: ₹${remaining}`;
                    this.remainingAmountDisplay.className = 'remaining-amount show valid';
                } else {
                    this.remainingAmountDisplay.className = 'remaining-amount';
                }
            }
        }

        updateCalculations() {
            const selectedShifts = Array.from(this.shiftCheckboxes).filter(cb => cb.checked);
            const discountAmount = parseFloat(this.discountInput.value) || 0;
            const billingUnits = parseInt(this.billingUnitsInput.value) || 1;

            if (selectedShifts.length === 0) {
                this.calculationCard.style.display = 'none';
                this.totalAmount = 0;
                return;
            }

            // Check if half-cycle is selected
            const isHalfCycle = document.querySelector('input[name="cycle_type"]:checked')?.value === 'half';

            // Calculate subtotal
            let subtotal = 0;
            selectedShifts.forEach(shift => {
                let price = parseFloat(shift.dataset.price) || 0;

                // Apply half-cycle premium if selected
                if (isHalfCycle) {
                    price = price / 2; // Half the period
                    price = price * 1.1; // Add 10% premium
                }

                subtotal += price * billingUnits;
            });

            // Calculate total
            const total = Math.max(0, subtotal - discountAmount);
            this.totalAmount = total;

            // Update display
            document.getElementById('subtotal').textContent = `₹${subtotal.toFixed(2)}`;
            document.getElementById('discount').textContent = `₹${discountAmount.toFixed(2)}`;
            document.getElementById('total').textContent = `₹${total.toFixed(2)}`;

            this.calculationCard.style.display = 'block';
            this.calculationCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

            // Update partial payment summary if partial payment is selected
            const isPartial = document.querySelector('input[name="payment_type"]:checked')?.value === 'partial';
            if (isPartial) {
                this.updatePartialPaymentSummary();
            }
        }

        setupCheckboxStyling() {
            // Style shift options with improved visual feedback
            this.shiftCheckboxes.forEach(checkbox => {
                const option = checkbox.closest('.shift-option');

                // Set initial state
                this.updateOptionStyle(option, checkbox.checked);

                checkbox.addEventListener('change', () => {
                    this.updateOptionStyle(option, checkbox.checked);
                });

                // Add click handler to the entire option for better UX
                option.addEventListener('click', (e) => {
                    if (e.target !== checkbox) {
                        checkbox.click();
                    }
                });
            });

            // Style month options with improved visual feedback
            this.monthCheckboxes.forEach(checkbox => {
                const option = checkbox.closest('.month-option');

                // Set initial state
                this.updateOptionStyle(option, checkbox.checked);

                checkbox.addEventListener('change', () => {
                    this.updateOptionStyle(option, checkbox.checked);
                });

                // Add click handler to the entire option for better UX
                option.addEventListener('click', (e) => {
                    if (e.target !== checkbox) {
                        checkbox.click();
                    }
                });
            });
        }

        updateOptionStyle(option, isSelected) {
            if (isSelected) {
                option.classList.add('selected');
            } else {
                option.classList.remove('selected');
            }
        }

        setupFormSubmission() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();

                // Validate required fields
                const requiredInputs = this.form.querySelectorAll('input[required], select[required]');
                let isFormValid = true;

                requiredInputs.forEach(input => {
                    if (!this.validateField(input)) {
                        isFormValid = false;
                    }
                });

                // Check if at least one shift is selected
                const selectedShifts = Array.from(this.shiftCheckboxes).filter(cb => cb.checked);
                if (selectedShifts.length === 0) {
                    isFormValid = false;
                    this.showNotification('Please select at least one shift.', 'error');
                }

                // Check billing units
                const billingUnits = parseInt(this.billingUnitsInput.value) || 0;
                if (billingUnits <= 0) {
                    isFormValid = false;
                    this.showNotification('Please enter a valid number of billing periods.', 'error');
                }

                // Check partial payment validation
                const isPartial = document.querySelector('input[name="payment_type"]:checked')?.value === 'partial';
                if (isPartial) {
                    const partialAmount = parseFloat(this.partialAmountInput.value) || 0;
                    const nextPaymentDate = document.getElementById('next_payment_date').value;

                    if (partialAmount <= 0) {
                        isFormValid = false;
                        this.showNotification('Please enter a valid partial payment amount.', 'error');
                    } else if (partialAmount >= this.totalAmount) {
                        isFormValid = false;
                        this.showNotification('Partial payment amount must be less than total amount.', 'error');
                    }

                    if (!nextPaymentDate) {
                        isFormValid = false;
                        this.showNotification('Please select next payment date for partial payment.', 'error');
                    }
                }

                if (isFormValid) {
                    this.submitForm();
                } else {
                    const firstError = this.form.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            });
        }

        submitForm() {
            const originalText = this.submitBtn.innerHTML;
            this.submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Invoice...';
            this.submitBtn.disabled = true;

            // Submit the form
            this.form.submit();
        }

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        window.invoiceManager = new InvoiceManager();

        // Allow back dating for invoice creation
        // No minimum date restriction to enable creating invoices for previous periods
        const dueDateInput = document.getElementById('due_date');
        if (dueDateInput) {
            // Remove any existing min attribute to allow back dates
            dueDateInput.removeAttribute('min');
        }
    });
</script>
{% endblock %}
