 {% extends "base.html" %}

{% block title %}Shift Management - Librainian{% endblock %}

{% block page_title %}Shift Management{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Shift Management</li>
{% endblock %}

{% block content %}
<div id="shifts-page-unique" class="shifts-content fade-in">

    <!-- Alert Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert-glass alert-{{ message.tags|default:'primary' }} alert-dismissible fade show mb-4" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' or message.tags == 'danger' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close-glass" data-bs-dismiss="alert" aria-label="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row g-4">
        <!-- Create/Edit Shift Form -->
        <div class="col-lg-4 col-md-6">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-{% if shift %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if shift %}Edit Shift{% else %}Create New Shift{% endif %}
                    </h5>
                </div>
                <div class="modern-card-body">
                    <form method="post" action="/{{role}}/shifts/" id="shiftForm">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="name" class="form-label-glass">
                                <i class="fas fa-tag me-2"></i>Shift Name
                            </label>
                            <input type="text"
                                   class="form-control-glass"
                                   id="name"
                                   name="name"
                                   value="{{ shift.name|default_if_none:'' }}"
                                   placeholder="e.g., Morning Shift"
                                   required>
                        </div>

                        <div class="mb-3">
                            <label for="time" class="form-label-glass">
                                <i class="fas fa-clock me-2"></i>Time Range
                            </label>
                            <input type="text"
                                   class="form-control-glass"
                                   id="time"
                                   name="time"
                                   value="{{ shift.time_range|default_if_none:'' }}"
                                   placeholder="e.g., 9:00 AM - 1:00 PM"
                                   required>
                        </div>

                        <div class="mb-4">
                            <label for="price" class="form-label-glass">
                                <i class="fas fa-rupee-sign me-2"></i>Base Monthly Rate (₹)
                            </label>
                            <input type="number"
                                   step="0.01"
                                   class="form-control-glass"
                                   id="price"
                                   name="price"
                                   value="{{ shift.price|default_if_none:'' }}"
                                   placeholder="0.00"
                                   required>
                            <div class="help-text mt-2" style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem;">
                                <i class="fas fa-info-circle me-1"></i>
                                Base monthly rate - daily and weekly rates calculated automatically
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn-glass-primary">
                                <i class="fas fa-{% if shift %}save{% else %}plus{% endif %} me-2"></i>
                                {% if shift %}Update Shift{% else %}Create Shift{% endif %}
                            </button>
                            {% if shift %}
                                <a href="/{{ role }}/shifts" class="btn-glass-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Shifts List -->
        <div class="col-lg-8 col-md-6">
            <div class="modern-card">
                <div class="modern-card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>All Shifts
                    </h5>
                </div>
                <div class="modern-card-body">
                    {% if shifts %}
                        <!-- Desktop Table View -->
                        <div class="table-responsive d-none d-md-block">
                            <table class="table-glass">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-tag me-2"></i>Name</th>
                                        <th><i class="fas fa-clock me-2"></i>Time Range</th>
                                        <th><i class="fas fa-rupee-sign me-2"></i>Rates</th>
                                        <th><i class="fas fa-cogs me-2"></i>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for shift in shifts %}
                                    <tr>
                                        <td>
                                            <strong style="color: white !important; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">{{ shift.name }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge-glass">{{ shift.time_range }}</span>
                                        </td>
                                        <td>
                                            <div class="rates-display">
                                                <div class="rate-item">
                                                    <span class="rate-label">Daily:</span>
                                                    <span class="price-glass">₹{{ shift.price|floatformat:0|add:0|div:30|floatformat:0 }}/day</span>
                                                </div>
                                                <div class="rate-item">
                                                    <span class="rate-label">Weekly:</span>
                                                    <span class="price-glass">₹{{ shift.price|floatformat:0|add:0|div:4.33|floatformat:0 }}/week</span>
                                                </div>
                                                <div class="rate-item">
                                                    <span class="rate-label">Monthly:</span>
                                                    <span class="price-glass">₹{{ shift.price }}/month</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group-glass" role="group">
                                                <a href="/{{ role }}/shifts/update/{{ shift.id }}"
                                                   class="btn-glass-warning btn-sm"
                                                   title="Edit Shift">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn-glass-danger btn-sm"
                                                        onclick="confirmDelete('{{ shift.id }}')"
                                                        title="Delete Shift">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Mobile Card View -->
                        <div class="mobile-shift-cards d-md-none">
                            {% for shift in shifts %}
                            <div class="mobile-shift-card">
                                <div class="mobile-shift-header">
                                    <div class="shift-info-mobile">
                                        <div class="shift-icon-mobile">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="shift-details-mobile">
                                            <h6 class="shift-name-mobile">{{ shift.name }}</h6>
                                            <span class="shift-time-mobile">{{ shift.time_range }}</span>
                                        </div>
                                    </div>
                                    <div class="shift-price-mobile">
                                        <div class="rates-display-mobile">
                                            <div class="rate-item-mobile">
                                                <span class="rate-label-mobile">Daily:</span>
                                                <span class="price-badge-mobile">₹{{ shift.price|floatformat:0|add:0|div:30|floatformat:0 }}</span>
                                            </div>
                                            <div class="rate-item-mobile">
                                                <span class="rate-label-mobile">Weekly:</span>
                                                <span class="price-badge-mobile">₹{{ shift.price|floatformat:0|add:0|div:4.33|floatformat:0 }}</span>
                                            </div>
                                            <div class="rate-item-mobile">
                                                <span class="rate-label-mobile">Monthly:</span>
                                                <span class="price-badge-mobile">₹{{ shift.price }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mobile-shift-body">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <div class="mobile-field">
                                                <label>Shift Name</label>
                                                <div class="mobile-value">
                                                    <i class="fas fa-tag me-2"></i>
                                                    {{ shift.name }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="mobile-field">
                                                <label>Time Range</label>
                                                <div class="mobile-value">
                                                    <i class="fas fa-clock me-2"></i>
                                                    {{ shift.time_range }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="mobile-field">
                                                <label>Price</label>
                                                <div class="mobile-value price-display">
                                                    <i class="fas fa-rupee-sign me-2"></i>
                                                    <span class="price-amount">₹{{ shift.price }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="mobile-field">
                                                <label>Actions</label>
                                                <div class="mobile-actions">
                                                    <a href="/{{ role }}/shifts/update/{{ shift.id }}"
                                                       class="btn-mobile-edit"
                                                       title="Edit Shift">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn-mobile-delete"
                                                            onclick="confirmDelete('{{ shift.id }}')"
                                                            title="Delete Shift">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state-glass">
                            <i class="fas fa-clock"></i>
                            <h5>No Shifts Created Yet</h5>
                            <p>Create your first shift to get started with managing library schedules.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content-glass">
                <div class="modal-header-glass">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                    </h5>
                    <button type="button" class="btn-close-glass" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body-glass">
                    <div class="text-center">
                        <i class="fas fa-trash-alt text-danger-glass" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <h6 class="text-white">Are you sure you want to delete this shift?</h6>
                        <p class="text-white-muted">This action cannot be undone. All associated data will be permanently removed.</p>
                    </div>
                </div>
                <div class="modal-footer-glass">
                    <button type="button" class="btn-glass-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="button" class="btn-glass-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-2"></i>Delete Shift
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Delete Form -->
    <form id="deleteForm" method="post" style="display:none;">
        {% csrf_token %}
    </form>
</div>

{% endblock %}

{% block extra_css %}
<style>
    /* Clean Shifts Theme */
    .shifts-content {
        min-height: 100vh;
        padding: 2rem;
        position: relative;
    }

    /* Remove padding on mobile */
    @media (max-width: 767.98px) {
        .shifts-content {
            padding: 0.5rem !important;
            min-height: calc(100vh - 120px);
        }
    }

    @media (max-width: 575.98px) {
        .shifts-content {
            padding: 0.25rem !important;
        }
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset,
            0 2px 4px rgba(255, 255, 255, 0.1) inset;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow:
            0 35px 60px -12px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.15) inset,
            0 2px 4px rgba(255, 255, 255, 0.15) inset;
    }

    .modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            transparent 100%);
        pointer-events: none;
        opacity: 0.8;
    }

    .modern-card-header {
        padding: 1.5rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
        background-color: rgba(255, 255, 255, 0.379);
    }

    .modern-card-body {
        padding: 2rem;
        position: relative;
        z-index: 2;
    }

    .page-title {
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .page-subtitle {
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .card-title {
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .stat-badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    /* Form Elements */
    .form-label-glass {
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control-glass {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: white;
        padding: 0.75rem 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        width: 100%;
        box-sizing: border-box;
    }

    .form-control-glass:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        outline: none;
    }

    .form-control-glass::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    /* Buttons */
    .btn-glass-primary {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        width: 100%;
    }

    .btn-glass-primary:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .btn-glass-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        width: 100%;
    }

    .btn-glass-secondary:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
    }

    .btn-glass-warning {
        background: rgba(255, 193, 7, 0.2);
        border: 1px solid rgba(255, 193, 7, 0.4);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
    }

    .btn-glass-warning:hover {
        background: rgba(255, 193, 7, 0.3);
        border-color: rgba(255, 193, 7, 0.6);
        color: white;
        transform: translateY(-1px);
    }

    .btn-glass-danger {
        background: rgba(220, 53, 69, 0.2);
        border: 1px solid rgba(220, 53, 69, 0.4);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-glass-danger:hover {
        background: rgba(220, 53, 69, 0.3);
        border-color: rgba(220, 53, 69, 0.6);
        color: white;
        transform: translateY(-1px);
    }

    /* Table */
    .table-glass {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        overflow: hidden;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        width: 100%;
    }

    .table-glass th {
        background: rgba(255, 255, 255, 0.2);
        color: white !important;
        font-weight: 600;
        border: none;
        padding: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .table-glass td {
        color: white !important;
        border-color: rgba(255, 255, 255, 0.1);
        padding: 1rem;
        vertical-align: middle;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .table-glass tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .badge-glass {
        background: rgba(255, 255, 255, 0.2);
        color: white !important;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 600;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .price-glass {
        color: #4ade80 !important;
        font-weight: 700;
        font-size: 1.1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .btn-group-glass {
        display: flex;
        gap: 0.5rem;
    }

    /* Empty State */
    .empty-state-glass {
        text-align: center;
        padding: 3rem 2rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .empty-state-glass i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.6;
        color: white;
    }

    .empty-state-glass h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .empty-state-glass p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 1rem;
    }

    /* Alerts */
    .alert-glass {
        background: rgba(255, 255, 255, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 16px;
        color: white;
        padding: 1rem 1.5rem;
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        position: relative;
        overflow: hidden;
    }

    .alert-glass::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 100%);
        pointer-events: none;
    }

    .alert-success {
        border-color: rgba(34, 197, 94, 0.5);
    }

    .alert-danger {
        border-color: rgba(239, 68, 68, 0.5);
    }

    .alert-warning {
        border-color: rgba(245, 158, 11, 0.5);
    }

    .btn-close-glass {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        color: white;
        padding: 0.5rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .btn-close-glass:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
    }

    /* Modal */
    .modal-content-glass {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    }

    .modal-header-glass {
        padding: 1.5rem 2rem 1rem 2rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
    }

    .modal-body-glass {
        padding: 2rem;
        color: white;
    }

    .modal-footer-glass {
        padding: 1rem 2rem 1.5rem 2rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .text-danger-glass {
        color: #ef4444 !important;
    }

    .text-white-muted {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Mobile Card Layout */
    .mobile-shift-cards {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .mobile-shift-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        -webkit-backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        padding: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .mobile-shift-card:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .mobile-shift-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 100%);
        pointer-events: none;
    }

    .mobile-shift-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        position: relative;
        z-index: 2;
    }

    .shift-info-mobile {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;
    }

    .shift-icon-mobile {
        width: 45px;
        height: 45px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .shift-details-mobile {
        display: flex;
        flex-direction: column;
    }

    .shift-name-mobile {
        color: white !important;
        font-weight: 700;
        font-size: 1rem;
        margin: 0 0 0.25rem 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .shift-time-mobile {
        color: rgba(255, 255, 255, 0.9) !important;
        font-size: 0.8rem;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .shift-price-mobile {
        flex-shrink: 0;
    }

    .price-badge-mobile {
        background: rgba(74, 222, 128, 0.3);
        color: #4ade80;
        padding: 0.5rem 1rem;
        border-radius: 12px;
        font-weight: 700;
        font-size: 1rem;
        border: 1px solid rgba(74, 222, 128, 0.5);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .mobile-shift-body {
        position: relative;
        z-index: 2;
    }

    .mobile-field {
        margin-bottom: 0.75rem;
    }

    .mobile-field label {
        display: block;
        color: rgba(255, 255, 255, 0.9) !important;
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .mobile-value {
        color: white !important;
        font-size: 0.85rem;
        font-weight: 500;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
    }

    .mobile-value i {
        color: rgba(255, 255, 255, 0.8) !important;
        width: 16px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .price-display .price-amount {
        color: #4ade80;
        font-weight: 700;
        font-size: 1rem;
    }

    .mobile-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-start;
    }

    .btn-mobile-edit,
    .btn-mobile-delete {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        font-size: 0.9rem;
        text-decoration: none;
    }

    .btn-mobile-edit {
        background: rgba(255, 193, 7, 0.3);
        border-color: rgba(255, 193, 7, 0.5);
        color: white;
    }

    .btn-mobile-edit:hover {
        background: rgba(255, 193, 7, 0.5);
        transform: scale(1.1);
        color: white;
    }

    .btn-mobile-delete {
        background: rgba(220, 53, 69, 0.3);
        border-color: rgba(220, 53, 69, 0.5);
        color: white;
    }

    .btn-mobile-delete:hover {
        background: rgba(220, 53, 69, 0.5);
        transform: scale(1.1);
        color: white;
    }



    /* Mobile Responsive */
    @media (max-width: 767.98px) {
        .shifts-content {
            padding: 0.5rem !important;
            min-height: calc(100vh - 120px);
        }

        .row.g-4 {
            margin: 0 !important;
            --bs-gutter-x: 0.5rem;
            --bs-gutter-y: 0.5rem;
        }

        .col-lg-4,
        .col-lg-8,
        .col-md-6 {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
        }

        .modern-card {
            margin-bottom: 0.5rem;
            border-radius: 16px;
        }

        .modern-card-body {
            padding: 1rem;
        }

        .modern-card-header {
            padding: 1rem 1rem 0.5rem 1rem;
        }

        .card-title {
            font-size: 1rem;
        }

        /* Mobile card improvements */
        .mobile-shift-cards {
            gap: 0.75rem;
            padding: 0;
        }

        .mobile-shift-card {
            padding: 0.75rem;
            border-radius: 12px;
            margin-bottom: 0.5rem;
        }

        .mobile-shift-header {
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .shift-icon-mobile {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .shift-name-mobile {
            font-size: 0.9rem;
        }

        .shift-time-mobile {
            font-size: 0.75rem;
        }

        .price-badge-mobile {
            font-size: 0.9rem;
            padding: 0.4rem 0.8rem;
        }

        .mobile-field {
            margin-bottom: 0.5rem;
        }

        .mobile-field label {
            font-size: 0.7rem;
            margin-bottom: 0.2rem;
        }

        .mobile-value {
            font-size: 0.8rem;
        }

        .price-display .price-amount {
            font-size: 0.9rem;
        }

        .btn-mobile-edit,
        .btn-mobile-delete {
            width: 32px;
            height: 32px;
            font-size: 0.8rem;
        }

        /* Form improvements on mobile */
        .form-control-glass {
            padding: 0.6rem 0.8rem;
            font-size: 0.9rem;
        }

        .btn-glass-primary,
        .btn-glass-secondary {
            padding: 0.6rem 1rem;
            font-size: 0.9rem;
        }
    }

    /* Extra small screens */
    @media (max-width: 575.98px) {
        .shifts-content {
            padding: 0.25rem !important;
        }

        .row.g-4 {
            --bs-gutter-x: 0.25rem;
            --bs-gutter-y: 0.25rem;
        }

        .col-lg-4,
        .col-lg-8,
        .col-md-6 {
            padding-left: 0.125rem !important;
            padding-right: 0.125rem !important;
        }

        .mobile-shift-cards {
            gap: 0.5rem;
        }

        .mobile-shift-card {
            padding: 0.5rem;
            border-radius: 10px;
        }

        .mobile-shift-header {
            margin-bottom: 0.5rem;
            padding-bottom: 0.4rem;
        }

        .shift-icon-mobile {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }

        .shift-name-mobile {
            font-size: 0.85rem;
        }

        .shift-time-mobile {
            font-size: 0.7rem;
        }

        .price-badge-mobile {
            font-size: 0.8rem;
            padding: 0.3rem 0.6rem;
        }

        .mobile-field {
            margin-bottom: 0.4rem;
        }

        .mobile-field label {
            font-size: 0.65rem;
            margin-bottom: 0.15rem;
        }

        .mobile-value {
            font-size: 0.75rem;
        }

        .price-display .price-amount {
            font-size: 0.85rem;
        }

        .btn-mobile-edit,
        .btn-mobile-delete {
            width: 28px;
            height: 28px;
            font-size: 0.75rem;
        }

        .form-control-glass {
            padding: 0.5rem 0.7rem;
            font-size: 0.85rem;
        }

        .btn-glass-primary,
        .btn-glass-secondary {
            padding: 0.5rem 0.8rem;
            font-size: 0.85rem;
        }
    }

    /* Rates Display Styles */
    .rates-display {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .rate-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .rate-label {
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
        min-width: 50px;
    }

    .price-glass {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        padding: 0.125rem 0.375rem;
        color: #fff;
        font-weight: 600;
        font-size: 0.75rem;
        white-space: nowrap;
    }

    /* Mobile rates display */
    .rates-display-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        align-items: flex-end;
    }

    .rate-item-mobile {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        font-size: 0.75rem;
    }

    .rate-label-mobile {
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
        font-size: 0.625rem;
    }

    .price-badge-mobile {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 6px;
        padding: 0.125rem 0.375rem;
        color: #fff;
        font-weight: 600;
        font-size: 0.625rem;
        white-space: nowrap;
    }

    /* Mobile responsive rates */
    @media (max-width: 767.98px) {
        .rates-display {
            gap: 0.125rem;
        }

        .rate-item {
            font-size: 0.75rem;
        }

        .rate-label {
            min-width: 40px;
        }

        .price-glass {
            font-size: 0.625rem;
            padding: 0.0625rem 0.25rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert-glass');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        });

        // Form validation with glass theme feedback
        const form = document.getElementById('shiftForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                const name = document.getElementById('name').value.trim();
                const time = document.getElementById('time').value.trim();
                const price = document.getElementById('price').value.trim();

                if (!name || !time || !price) {
                    e.preventDefault();
                    showGlassAlert('Please fill in all required fields.', 'warning');
                    return false;
                }

                if (parseFloat(price) < 0) {
                    e.preventDefault();
                    showGlassAlert('Price cannot be negative.', 'danger');
                    return false;
                }

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                submitBtn.disabled = true;
            });
        }

        // Enhanced form interactions
        const inputs = document.querySelectorAll('.form-control-glass');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
            });

            input.addEventListener('blur', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '';
            });
        });
    });

    // Glass alert function
    function showGlassAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert-glass alert-${type} fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close-glass" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        const container = document.querySelector('.shifts-content');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            alertDiv.style.opacity = '0';
            alertDiv.style.transform = 'translateY(-20px)';
            setTimeout(() => alertDiv.remove(), 300);
        }, 5000);
    }

    // Delete confirmation with glass modal
    function confirmDelete(shiftId) {
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
        document.getElementById('confirmDeleteBtn').setAttribute('data-shift-id', shiftId);
    }

    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        const shiftId = this.getAttribute('data-shift-id');
        const deleteForm = document.getElementById('deleteForm');
        deleteForm.action = '/{{ role }}/shifts/delete/' + shiftId + '/';

        // Show loading state
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';
        this.disabled = true;

        deleteForm.submit();
    });
</script>
{% endblock %}